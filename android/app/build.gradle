plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id 'com.google.gms.google-services'
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}


android {
    namespace "com.eljunto"
    compileSdk flutter.compileSdkVersion
    ndkVersion "29.0.13599879"

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.eljunto"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 28
        targetSdkVersion flutter.targetSdkVersion
        compileSdkVersion flutter.compileSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

    flavorDimensions "default"
    productFlavors {
        prod {
            dimension "default"
            resValue "string", "app_name", "El Junto"
        }
        dev {
            dimension "default"
            applicationIdSuffix ".dev"
            resValue "string", "app_name", "El Junto Dev"
            versionNameSuffix ".dev"
        }
    }// Add prod and dev field 11 Dec 2024


    signingConfigs {

        // debug {    
        //     storeFile file("C:/Users/<USER>/.android/debug.keystore")
        //     storePassword "android"    
        //     keyAlias "androiddebugkey"    
        //     keyPassword "android"
        // }
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            //   signingConfig signingConfigs.debug
            signingConfig signingConfigs.release
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    def billing_version = '7.1.1'
    implementation "com.android.billingclient:billing:$billing_version"
    // implementation 'com.google.android.gms:play-services:18.0.1' // 17.0.0
    // implementation 'com.android.support:appcompat-v7:28.0.0'
    // classpath 'com.google.gms:google-services:4.3.15' // ADD THIS LINE 25 Dec 2024

    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'
}
