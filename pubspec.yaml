name: eljunto
description: "A new Flutter project."
publish_to: "none" # Remove this line if you wish to publish to pub.dev
version: 1.0.6+111

environment:
  sdk: ">=3.3.4 <4.0.0"

dependencies:
  agora_rtc_engine: ^6.5.2
  animated_custom_dropdown: ^3.1.1
  app_badge_plus: ^1.2.3
  app_links: ^6.4.0
  app_settings: ^6.1.1
  cached_network_image: ^3.4.1
  cloud_firestore: ^6.0.0
  connectivity_plus: ^6.1.4
  crypto: ^3.0.6
  device_info_plus: ^11.5.0
  dio: ^5.8.0+1
  email_validator: ^3.0.0
  emoji_picker_flutter: ^4.3.0
  firebase_analytics: ^12.0.0
  firebase_core: ^4.0.0
  firebase_in_app_messaging: ^0.9.0
  firebase_messaging: ^16.0.0
  firebase_storage: ^13.0.0

  flutter:
    sdk: flutter
  flutter_advanced_switch: ^3.1.0
  flutter_image_compress: ^2.4.0
  flutter_local_notifications: ^19.3.0
  flutter_rating_bar: ^4.0.1
  flutter_staggered_grid_view: ^0.7.0
  flutter_svg: ^2.2.0
  flutter_typeahead: ^5.2.0
  get_it: ^8.0.3
  go_router: ^16.0.0
  google_fonts: ^6.2.1
  googleapis_auth: ^2.0.0
  http: ^1.4.0
  image_cropper: ^9.1.0
  image_picker: ^1.1.2
  in_app_purchase: ^3.2.3
  in_app_purchase_android: ^0.4.0
  in_app_purchase_storekit: ^0.4.3
  internet_connection_checker_plus: ^2.7.2
  intl: ^0.20.2
  month_picker_dialog: ^6.3.0
  package_info_plus: ^8.3.0
  path: ^1.9.1
  path_provider: ^2.1.5
  permission_handler: ^12.0.1
  pinput: ^5.0.1
  provider: ^6.1.5
  roundcheckbox: ^2.0.5
  sembast: ^3.8.5+1
  shared_preferences: ^2.5.3
  skeletonizer: ^2.1.0
  sqflite: ^2.4.2
  url_launcher: ^6.3.2
  wakelock_plus: ^1.3.2
  webview_flutter: ^4.13.0

dev_dependencies:
  change_app_package_name: ^1.5.0
  dependency_validator: ^5.0.2
  flutter_launcher_icons: ^0.14.4
  flutter_lints: ^6.0.0

  flutter_test:
    sdk: flutter
  pubspec_dependency_sorter: ^1.0.5

flutter:
  uses-material-design: true
  assets:
    - assets/el-junto-4fb80-firebase-adminsdk-gwtik-f421cef23d.json
    - assets/icons/svg/
    - assets/icons/
    - assets/images/
    - assets/images/ads/
    - assets/images/bottom_navigation_icon/
    - assets/images/bottom_navigation_icon/new_icons/
    - assets/images/ElJuntoBookClubLogoSvg.svg
    - assets/el-junto-development-server-firebase-adminsdk-vmec3-eb3a739632.json
    - assets/el-junto-4fb80-firebase-adminsdk-gwtik-f421cef23d.json
    - assets/config/currency_code.json
  fonts:
    - family: Libre Baskerville Regular
      fonts:
        - asset: assets\fonts\LibreBaskerville-Regular.ttf
    - family: Libre Baskerville Italic
      fonts:
        - asset: assets\fonts\LibreBaskerville-Italic.ttf
    - family: Libre Baskerville Bold
      fonts:
        - asset: assets\fonts\LibreBaskerville-Bold.ttf
