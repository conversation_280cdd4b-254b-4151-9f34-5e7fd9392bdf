import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart' as dio;
import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/models/profile_model/edit_bookcase/listof_book_model.dart';
import 'package:eljunto/models/profile_model/location_model.dart';
import 'package:flutter/material.dart';

import '../models/profile_model/edit_profile/update_user_profile_model.dart';
import '../services/user_service.dart';

class ProfileController with ChangeNotifier {
  final UserService userService = UserService();
  final _sessionManager = locator<SessionManager>();

  /// PROFILE UPDATE FUNCTION
  bool isUpdate = false;
  String apiResponse = '';
  Future<bool> updateProfileFunction(
      UserProfileUpdateModel profile, BuildContext context) async {
    try {
      dio.Response response = await userService.updateProfileFunction(profile);

      if (response.statusCode == 200) {
        log(response.data.toString());
        apiResponse = response.data['message'];
        isUpdate = true;
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await _sessionManager.userLogout(context, payload);
        }
        notifyListeners();
      } else {
        log("Failed : ${response.data}");
        apiResponse = response.data['message'];
        isUpdate = false;
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      log("Error : ${e.toString()}");
      isUpdate = false;
    }
    return isUpdate;
  }

  /// GET ALL BOOKS
  AllBookModel? allBookModel;
  int bookCount = 0;
  Future<List<Books>?> allBookFunction(int loggedInUserId, String search,
      int page, int limit, BuildContext context) async {
    List<Books>? booksData = [];
    try {
      dio.Response response = await userService.allBookFunction(
          loggedInUserId, search, page, limit);

      if (response.statusCode == 200) {
        var jsonObject = response.data;
        if (jsonObject.containsKey('data') && jsonObject['data'] != null) {
          allBookModel = AllBookModel.fromJson(jsonObject);
          bookCount = jsonObject['count'];

          booksData = allBookModel?.data;
        } else {
          allBookModel = null;
          booksData = [];
          log("Failed access : ${response.data}");
        }
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await _sessionManager.userLogout(context, payload);
        }
        notifyListeners();
      } else {
        allBookModel = null;
        booksData = [];
        log("Can't fetch data : ${response.data}");
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      allBookModel = null;
      booksData = [];
      log("Error1 : ${e.toString()}");
    }
    return booksData;
  }

  LocationModel? locationModel;
  Future<List<Location>?> getLocationData(
      String search, BuildContext context) async {
    List<Location>? locationList = [];

    try {
      dio.Response response = await userService.getLocationData(search);
      if (response.statusCode == 200) {
        var jsonObject = response.data;
        if (jsonObject.containsKey('data') && jsonObject['data'] != null) {
          locationModel = LocationModel.fromJson(jsonObject);
          locationList = locationModel?.data;
        } else {
          locationModel = null;
          locationList = [];
          log("Failed access : ${response.data}");
        }
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await _sessionManager.userLogout(context, payload);
        }
        notifyListeners();
      } else {
        locationModel = null;
        locationList = [];
        log("Can't fetch data : ${response.data}");
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      locationModel = null;
      locationList = [];
      log(e.toString());
    }
    return locationList;
  }
}
