import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart' as dio;
import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/models/book_case_model.dart';
import 'package:eljunto/services/book_case_service.dart';
import 'package:flutter/material.dart';

class BookCaseController with ChangeNotifier {
  final BookCaseService bookCaseService = BookCaseService();
  String errorMessage = '';
  List<BookCaseModel>? profileBookCaseModel;
  bool isCurrentReadBookLoading = false;

  List<BookCaseModel> bookCaseList = [];

  List<BookCaseModel> get item => bookCaseList;
  final _sessionManager = locator<SessionManager>();

  Future<Map<String, dynamic>> getCurrentReadBookCase(
      int userId, int limit, int offSet, BuildContext context) async {
    Map<String, dynamic> responseMap = {};
    isCurrentReadBookLoading = true;
    // notifyListeners();
    try {
      dio.Response response =
          await bookCaseService.getCurrentReadBookCase(userId, limit, offSet);

      if (response.statusCode == 200) {
        responseMap = response.data;
      } else if (response.statusCode == 401) {
        if (context.mounted) {
          Map<String, dynamic> payload = {
            "errorMessage": response.data,
            "errorCode": response.statusCode,
            "errorLevel": '',
            "source": "bookCase/currently-reading",
            "stackTrace": '',
            "requestData": {
              "userId": userId,
              "limit": limit,
              "offset": offSet,
            },
            "userId": userId,
          };
          await _sessionManager.userLogout(
            context,
            payload,
          );
        }
        notifyListeners();
      } else {
        final jsonResponse = response.data;
        errorMessage = jsonResponse['message'] ?? 'Something went wrong';
        // errorMessage = 'Something went wrong';
        responseMap = {"error": errorMessage};
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      errorMessage = 'Something went wrong';
      log(errorMessage);
      responseMap = {"error": errorMessage};
    }
    isCurrentReadBookLoading = false;
    notifyListeners();
    return responseMap;
  }

  Future<Map<String, dynamic>> getToBeReadBook(
      int userId, int limit, int offSet, BuildContext context) async {
    Map<String, dynamic> responseMap = {};
    isCurrentReadBookLoading = true;
    // notifyListeners();
    try {
      dio.Response response =
          await bookCaseService.getToBeReadBook(userId, limit, offSet);

      if (response.statusCode == 200) {
        responseMap = response.data;
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {
          "errorMessage": response.data,
          "errorCode": response.statusCode,
          "errorLevel": '',
          "source": "bookCase/currently-reading",
          "stackTrace": '',
          "requestData": {
            "userId": userId,
            "limit": limit,
            "offset": offSet,
          },
          "userId": userId,
        };
        if (context.mounted) {
          await _sessionManager.userLogout(context, payload);
        }
        notifyListeners();
      } else {
        final jsonResponse = response.data;
        errorMessage = jsonResponse['message'] ?? 'Something went wrong';
        // errorMessage = 'Something went wrong';
        responseMap = {"error": errorMessage};
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      errorMessage = 'Something went wrong';
      log(errorMessage);
      responseMap = {"error": errorMessage};
    }
    isCurrentReadBookLoading = false;
    notifyListeners();
    return responseMap;
  }

  bool isTopShelfBookLoading = false;

  Future<Map<String, dynamic>> allBooksRead(int userId, int limit, int offSet,
      bool topShelf, bool completeReading, BuildContext context) async {
    Map<String, dynamic> responseMap = {};
    isTopShelfBookLoading = true;
    // notifyListeners();
    try {
      dio.Response response = await bookCaseService.allBooksRead(
          userId, limit, offSet, completeReading);

      if (response.statusCode == 200) {
        responseMap = response.data;
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {
          "errorMessage": response.data,
          "errorCode": response.statusCode,
          "errorLevel": '',
          "source": "bookCase/currently-reading",
          "stackTrace": '',
          "requestData": {
            "userId": userId,
            "limit": limit,
            "offset": offSet,
          },
          "userId": userId,
        };
        if (context.mounted) {
          await _sessionManager.userLogout(context, payload);
        }
        notifyListeners();
      } else {
        final jsonResponse = response.data;
        errorMessage = jsonResponse['message'] ?? 'Something went wrong';
        responseMap = {"error": errorMessage};
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      errorMessage = 'Something went wrong';
      log(errorMessage);
      responseMap = {"error": errorMessage};
    }
    isTopShelfBookLoading = false;
    notifyListeners();
    return responseMap;
  }

  Future<Map<String, dynamic>> getBookCase(int userId, int limit, int offSet,
      bool topShelf, bool completeReading, BuildContext context) async {
    Map<String, dynamic> responseMap = {};

    try {
      // if (profileBookCaseModel != null) return;
      dio.Response response = await bookCaseService.allBooksRead(
          userId, limit, offSet, completeReading);
      if (response.statusCode == 200) {
        responseMap = response.data;
        if (responseMap.containsKey('data') && responseMap['data'] != null) {
          profileBookCaseModel = (responseMap["data"] as List)
              .map((item) => BookCaseModel.fromJson(item))
              .toList();
        } else {
          profileBookCaseModel = null;
        }
        notifyListeners();
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {
          "errorMessage": response.data,
          "errorCode": response.statusCode,
          "errorLevel": '',
          "source": "bookCase/currently-reading",
          "stackTrace": '',
          "requestData": {
            "userId": userId,
            "limit": limit,
            "offset": offSet,
          },
          "userId": userId,
        };
        if (context.mounted) {
          await _sessionManager.userLogout(context, payload);
        }
        notifyListeners();
      } else {
        profileBookCaseModel = null;
        log('Failed to fetch data : ${response.data}');
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      log(e.toString());
    }
    return responseMap;
  }

  String? bookAddStatus;
  String? addBookErrorMessage;
  int? bookCaseId;
  BookCaseModel? originalBookModel;

  Future<String?> addBookInBookCase(
      BookCaseModel book, BuildContext context) async {
    try {
      dio.Response response = await bookCaseService.addBookInBookCase(book);
      log('response message: ${response.data}}');

      if (response.statusCode == 201) {
        log('Book Add in current read : ${response.data}');
        bookAddStatus = 'added';
      } else if (response.statusCode == 400) {
        log("Book Add in current read error : ${response.data}");
        final jsonResponse = response.data;
        addBookErrorMessage = response.data['message'];
        if (jsonResponse.containsKey('data') && jsonResponse['data'] != null) {
          bookCaseId = jsonResponse['data']['bookcase_id'];
          log("ResponseMessage : $addBookErrorMessage");
          log("Book Case Id : $bookCaseId");
        }
        originalBookModel = book;
        notifyListeners();
        log("Book Already Present");
        bookAddStatus = 'exist';
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await _sessionManager.userLogout(context, payload);
        }
        notifyListeners();
      } else {
        log('Failed  : ${response.data}');
        bookAddStatus = 'error';
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      log(e.toString());
      bookAddStatus = 'error';
    }
    return bookAddStatus;
  }

  bool isUpdate = false;

  Future<bool> updateBookCase(BookCaseModel obj, BuildContext context) async {
    try {
      dio.Response response = await bookCaseService.updateBookCase(obj);
      log('Update Book Case : ${response.data}');
      if (response.statusCode == 200) {
        isUpdate = true;
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await _sessionManager.userLogout(context, payload);
        }
        isUpdate = false;
        notifyListeners();
      } else {
        isUpdate = false;
        log("Failed Update Book Case : ${response.data}");
        final jsonResponse = response.data;
        errorMessage = jsonResponse['message'] ?? 'Something went wrong';
        // errorMessage = 'Something went wrong';
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      isUpdate = false;
      log(e.toString());
      errorMessage = 'Something went wrong';
      log(errorMessage);
    }
    return isUpdate;
  }

  bool isDeleteBookLoading = false;

  Future<bool> deleteBook(int? bookId, BuildContext context) async {
    try {
      dio.Response response = await bookCaseService.deleteBook(bookId);
      if (response.statusCode == 200) {
        log('Delete Successful : ${response.data}');
        isDeleteBookLoading = true;
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await _sessionManager.userLogout(context, payload);
        }
        notifyListeners();
      } else {
        log('Delete Failed : ${response.data}');
        isDeleteBookLoading = false;
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      log(e.toString());
      isDeleteBookLoading = false;
    }
    return isDeleteBookLoading;
  }

  bool _isTypeAheadEmpty = false;

  bool get isTypeAheadEmpty => _isTypeAheadEmpty;

  Future<void> updateTypeAheadFlag(bool value) async {
    log("Updated Flag : $value");
    _isTypeAheadEmpty = value;
    notifyListeners();
  }

  bool _isKeyboardVisible = false;

  bool get isKeyboardVisible => _isKeyboardVisible;

  void updateKeyboardVisibility(bool isVisible) {
    _isKeyboardVisible = isVisible;
    notifyListeners();
  }
}
