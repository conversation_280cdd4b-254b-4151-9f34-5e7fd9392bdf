import 'package:eljunto/app/core/widgets/app_button.dart';
import 'package:eljunto/app/core/widgets/custom_scaffold.dart';
import 'package:eljunto/app/features/authentication/providers/auth_provider.dart';
import 'package:eljunto/app/features/authentication/views/widgets/auth_footer.dart';
import 'package:eljunto/app/features/authentication/views/widgets/auth_header.dart';
import 'package:eljunto/app/features/authentication/views/widgets/auth_otp_field.dart';
import 'package:eljunto/app/features/authentication/views/widgets/validation_display.dart';
import 'package:eljunto/reusableWidgets/connection_error/no_connection_tag.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

/// Enhanced OTP Verification Screen following new architecture patterns
class OtpVerificationScreen extends StatefulWidget {
  final String email;
  final String token;
  final bool isForgotPassword;

  const OtpVerificationScreen({
    super.key,
    required this.email,
    required this.token,
    this.isForgotPassword = false,
  });

  @override
  State<OtpVerificationScreen> createState() => _OtpVerificationScreenState();
}

class _OtpVerificationScreenState extends State<OtpVerificationScreen> {
  final _otpController = TextEditingController();
  final _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // Start the OTP timer
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AuthProvider>().startTimer();
    });
  }

  @override
  void dispose() {
    _otpController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _clearValidationMessage() {
    context.read<AuthProvider>().clearError();
  }

  Future<void> _handleVerifyOtp() async {
    if (_otpController.text.length != 6) {
      return;
    }

    await context.read<AuthProvider>().verifyOtp(
          widget.email,
          _otpController.text,
          widget.isForgotPassword,
          widget.token,
          context,
        );
  }

  Future<void> _handleResendOtp() async {
    // Implement resend OTP logic here
    // This would typically call the email verification API again
    context.read<AuthProvider>().startTimer();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Stack(
          children: [
            CustomScaffold(
              backgroundColor: Colors.transparent,
              appBar: AuthHeader(
                onBackPressed: () => context.pop(),
              ),
              body: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          const SizedBox(height: 24),

                          // Title
                          AuthTitle(title: 'Verify Your Email'),

                          const SizedBox(height: 36),

                          // OTP Input
                          Text('Enter verification code sent to email:'),

                          const SizedBox(height: 10),
                          AuthOtpField(
                            otpController: _otpController,
                            focusNode: _focusNode,
                            isFieldEnabled: authProvider.isFieldEnabled,
                            onChanged: (_) => _clearValidationMessage(),
                          ),

                          const SizedBox(height: 5),

                          // Timer and Resend
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // Error Message
                              ValidationDisplay(
                                message: authProvider.errorMessage,
                                isVisible: authProvider.errorMessage.isNotEmpty,
                              ),
                              Align(
                                alignment: Alignment.centerRight,
                                child: OtpTimerDisplay(
                                  timerCountdown: authProvider.timerCountdown,
                                  canResend: authProvider.isResendOtp,
                                  onResend: authProvider.isFieldEnabled
                                      ? _handleResendOtp
                                      : null,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 25),

                          // Verify Button
                          AppButton(
                            text: 'Next',
                            height: 44,
                            borderRadius: 36,
                            onPressed: authProvider.isFieldEnabled &&
                                    _otpController.text.length == 6
                                ? _handleVerifyOtp
                                : null,
                            isLoading: authProvider.otpLoading,
                            isEnabled: authProvider.isFieldEnabled,
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Contact Us
                  const AuthFooter(),
                ],
              ),
            ),

            // No Connection Tag
            const NoConnectionTag(bottomPosition: 70),
          ],
        );
      },
    );
  }
}
