import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

/// Validation Message Display Widget
class ValidationDisplay extends StatelessWidget {
  final String? message;
  final bool isVisible;
  final Color? textColor;
  final IconData? icon;
  final EdgeInsetsGeometry? padding;

  const ValidationDisplay({
    super.key,
    this.message,
    this.isVisible = true,
    this.textColor,
    this.icon,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible || message == null || message!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: padding ?? const EdgeInsets.only(top: 8, left: 20, right: 20),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              size: 16,
              color: textColor ?? Colors.red,
            ),
            const SizedBox(width: 8),
          ],
          Expanded(
            child: Text(
              message!,
              style: lbRegular.copyWith(
                fontSize: 12,
                color: textColor ?? Colors.red,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Password Strength Indicator
class PasswordStrengthIndicator extends StatelessWidget {
  final bool isPasswordValid;
  final bool isPasswordComplex;
  final bool? isPasswordMatch;
  final bool showValidation;
  final bool? showMatchValidation;

  const PasswordStrengthIndicator({
    super.key,
    required this.isPasswordValid,
    required this.isPasswordComplex,
    this.isPasswordMatch,
    this.showValidation = true,
    this.showMatchValidation,
  });

  @override
  Widget build(BuildContext context) {
    if (!showValidation) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          PasswordRequirement(
            text: '8 characters',
            isMet: isPasswordValid,
          ),
          const SizedBox(height: 10),
          PasswordRequirement(
            text:
                '3 of the following: Capital letter, lowercase letter, number or symbol',
            isMet: isPasswordComplex,
          ),
          if (showMatchValidation ?? false) ...[
            const SizedBox(height: 10),
            PasswordRequirement(
              text: 'Matches',
              isMet: isPasswordMatch ?? false,
            ),
          ],
        ],
      ),
    );
  }
}

class PasswordRequirement extends StatelessWidget {
  final String text;
  final bool isMet;

  const PasswordRequirement({
    super.key,
    required this.text,
    required this.isMet,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          isMet ? Icons.check_circle : Icons.cancel,
          size: 16,
          color: isMet ? Colors.green : Colors.red,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: lbItalic.copyWith(
              fontSize: 12,
              color: isMet ? Colors.green : Colors.red,
            ),
          ),
        ),
      ],
    );
  }
}

/// Terms and Conditions Checkbox
class TermsCheckbox extends StatelessWidget {
  final bool isChecked;
  final ValueChanged<bool?> onChanged;
  final String text;
  final String? linkText;
  final VoidCallback? onLinkTap;
  final String? secondText;
  final String? secondLinkText;
  final VoidCallback? onSecondLinkTap;
  final bool showValidation;
  final String? validationMessage;

  const TermsCheckbox({
    super.key,
    required this.isChecked,
    required this.onChanged,
    required this.text,
    this.linkText,
    this.onLinkTap,
    this.secondText,
    this.secondLinkText,
    this.onSecondLinkTap,
    this.showValidation = false,
    this.validationMessage,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            GestureDetector(
              onTap: () => onChanged(!isChecked),
              child: Icon(
                isChecked ? Icons.check_circle : Icons.circle_outlined,
                size: 25,
                color: isChecked
                    ? AppConstants.textGreenColor
                    : AppConstants.primaryColor,
              ),
            ),
            const SizedBox(width: 10),
            Expanded(
              child: GestureDetector(
                onTap: () => onChanged(!isChecked),
                child: RichText(
                  text: TextSpan(
                    style: lbRegular.copyWith(
                      fontSize: 12,
                      color: AppConstants.primaryColor,
                    ),
                    children: [
                      TextSpan(text: text),
                      TextSpan(
                        text: linkText,
                        style: lbRegular.copyWith(
                          fontSize: 12,
                          color: AppConstants.blueColor,
                        ),
                        recognizer: onLinkTap != null
                            ? (TapGestureRecognizer()..onTap = onLinkTap)
                            : null,
                      ),
                      if (secondText != null) TextSpan(text: secondText),
                      if (secondLinkText != null)
                        TextSpan(
                          text: secondLinkText,
                          style: lbRegular.copyWith(
                            fontSize: 12,
                            color: AppConstants.blueColor,
                          ),
                          recognizer: onSecondLinkTap != null
                              ? (TapGestureRecognizer()
                                ..onTap = onSecondLinkTap)
                              : null,
                        ),
                      TextSpan(
                        text: ' *',
                        style: lbRegular.copyWith(color: AppConstants.redColor),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        if (showValidation && validationMessage != null)
          ValidationDisplay(
            message: validationMessage,
            textColor: AppConstants.redColor,
            padding: const EdgeInsets.only(top: 4),
          ),
      ],
    );
  }
}

/// OTP Timer Display
class OtpTimerDisplay extends StatelessWidget {
  final ValueNotifier<int> timerCountdown;
  final bool canResend;
  final VoidCallback? onResend;

  const OtpTimerDisplay({
    super.key,
    required this.timerCountdown,
    required this.canResend,
    this.onResend,
  });

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<int>(
      valueListenable: timerCountdown,
      builder: (context, timer, child) {
        if (canResend) {
          return TextButton(
            onPressed: onResend,
            child: Text(
              'Resend OTP',
              style: lbRegular.copyWith(
                fontSize: 12,
                color: AppConstants.primaryColor,
              ),
            ),
          );
        }

        final minutes = timer ~/ 60;
        final seconds = timer % 60;
        final timeString =
            '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';

        return Text.rich(
          TextSpan(
            text: 'Resend OTP in ',
            style: lbRegular.copyWith(
              fontSize: 12,
              color: AppConstants.primaryColor.withValues(alpha: 0.7),
            ),
            children: [
              TextSpan(
                text: timeString,
                style: lbBold.copyWith(
                  fontSize: 12,
                  decoration: TextDecoration.underline,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
