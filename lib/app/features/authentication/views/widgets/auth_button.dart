import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/app/core/widgets/app_button.dart';
import 'package:flutter/material.dart';

/// Reusable Authentication Button Widget
// class AuthButton extends StatelessWidget {
//   final String text;
//   final VoidCallback? onPressed;
//   final bool isLoading;
//   final bool isEnabled;
//   final double? width;
//   final double height;
//   final double borderRadius;
//   final Color? backgroundColor;
//   final Color? textColor;
//   final Color? disabledColor;
//   final Widget? child;
//   final EdgeInsetsGeometry? padding;
//
//   const AuthButton({
//     super.key,
//     required this.text,
//     this.onPressed,
//     this.isLoading = false,
//     this.isEnabled = true,
//     this.width,
//     this.height = 50,
//     this.borderRadius = 30,
//     this.backgroundColor,
//     this.textColor,
//     this.disabledColor,
//     this.child,
//     this.padding,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     final bool canPress = isEnabled && !isLoading && onPressed != null;
//
//     return Container(
//       width: isLoading ? height : (width ?? MediaQuery.of(context).size.width),
//       height: height,
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(borderRadius),
//         gradient: canPress
//             ? const LinearGradient(
//                 colors: [
//                   AppConstants.textGreenColor,
//                   AppConstants.textGreenColor,
//                 ],
//               )
//             : null,
//         color: !canPress
//             ? (disabledColor ??
//                 AppConstants.primaryColor.withValues(alpha: 0.3))
//             : null,
//       ),
//       child: Material(
//         color: Colors.transparent,
//         child: InkWell(
//           borderRadius: BorderRadius.circular(borderRadius),
//           onTap: canPress ? onPressed : null,
//           child: Container(
//             padding: padding ?? const EdgeInsets.symmetric(horizontal: 20),
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(borderRadius),
//             ),
//             child: Center(
//               child: isLoading
//                   ? SizedBox(
//                       width: 24,
//                       height: 24,
//                       child: CircularProgressIndicator(
//                         valueColor: AlwaysStoppedAnimation<Color>(
//                           textColor ?? Colors.white,
//                         ),
//                         strokeWidth: 3.0,
//                       ),
//                     )
//                   : child ??
//                       Text(
//                         text,
//                         style: lbBold.copyWith(
//                           fontSize: 18,
//                           color: textColor ?? AppConstants.primaryColor,
//                         ),
//                         textAlign: TextAlign.center,
//                       ),
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }

/// Authentication Button with Icon
class AuthIconButton extends StatelessWidget {
  final String text;
  final IconData icon;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isEnabled;
  final double? width;
  final double height;
  final double borderRadius;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? iconColor;

  const AuthIconButton({
    super.key,
    required this.text,
    required this.icon,
    this.onPressed,
    this.isLoading = false,
    this.isEnabled = true,
    this.width,
    this.height = 50,
    this.borderRadius = 30,
    this.backgroundColor,
    this.textColor,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return AppButton(
      text: text,
      onPressed: onPressed,
      isLoading: isLoading,
      isEnabled: isEnabled,
      width: width,
      height: height,
      borderRadius: borderRadius,
      textColor: textColor,
      child: isLoading
          ? null
          : Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: iconColor ?? textColor ?? AppConstants.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  text,
                  style: lbBold.copyWith(
                    fontSize: 18,
                    color: textColor ?? AppConstants.primaryColor,
                  ),
                ),
              ],
            ),
    );
  }
}

/// Text Button for Authentication
class AuthTextButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final Color? textColor;
  final double fontSize;
  final FontWeight fontWeight;
  final TextStyle textStyle;

  const AuthTextButton({
    super.key,
    required this.text,
    required this.textStyle,
    this.onPressed,
    this.textColor,
    this.fontSize = 16,
    this.fontWeight = FontWeight.normal,
  });

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: onPressed,
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        minimumSize: Size.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      child: Text(
        text,
        style: textStyle,
      ),
    );
  }
}
