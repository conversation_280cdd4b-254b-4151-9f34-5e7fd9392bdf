import 'package:flutter/material.dart';
import 'package:pinput/pinput.dart';

import '../../../../core/constants.dart';
import '../../../../core/utils/text_style.dart';
import '../../utils/auth_utils.dart';

class AuthOtpField extends StatelessWidget {
  final TextEditingController otpController;
  final FocusNode focusNode;
  final bool isFieldEnabled;
  final String? errorMessage;
  final Function(String)? onChanged;

  const AuthOtpField({
    super.key,
    required this.otpController,
    required this.focusNode,
    this.isFieldEnabled = true,
    this.errorMessage,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final defaultPinTheme = PinTheme(
      width: 42,
      height: 42,
      textStyle: lbBold.copyWith(
        fontSize: 20,
        color: AppConstants.primaryColor,
      ),
      decoration: BoxDecoration(
        border: Border.all(
          color: AppConstants.hintTextColor,
          width: 1.5,
        ),
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
    );

    final focusedPinTheme = defaultPinTheme.copyDecorationWith(
      border: Border.all(
        color: AppConstants.primaryColor,
        width: 1.5,
      ),
    );

    final submittedPinTheme = defaultPinTheme.copyWith(
      decoration: defaultPinTheme.decoration?.copyWith(
        color: AppConstants.textGreenColor.withValues(alpha: 0.1),
      ),
    );

    return Pinput(
      controller: otpController,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      focusNode: focusNode,
      length: 6,
      enabled: isFieldEnabled,
      defaultPinTheme: defaultPinTheme,
      focusedPinTheme: focusedPinTheme,
      submittedPinTheme: submittedPinTheme,
      pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
      showCursor: true,
      onChanged: onChanged,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter the OTP';
        }
        if (!AuthValidators.validateOtp(value)) {
          return 'Please enter a valid 6-digit OTP';
        }
        return null;
      },
    );
  }
}
