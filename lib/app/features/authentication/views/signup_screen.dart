import 'package:eljunto/app/core/widgets/app_button.dart';
import 'package:eljunto/app/core/widgets/custom_scaffold.dart';
import 'package:eljunto/app/features/authentication/providers/auth_provider.dart';
import 'package:eljunto/app/features/authentication/utils/auth_utils.dart';
import 'package:eljunto/app/features/authentication/views/widgets/auth_footer.dart';
import 'package:eljunto/app/features/authentication/views/widgets/auth_form_field.dart';
import 'package:eljunto/app/features/authentication/views/widgets/auth_header.dart';
import 'package:eljunto/app/features/authentication/views/widgets/validation_display.dart';
import 'package:eljunto/reusableWidgets/connection_error/no_connection_tag.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../core/constants.dart';

/// Enhanced Signup Screen following new architecture patterns
class SignupScreen extends StatefulWidget {
  const SignupScreen({super.key});

  @override
  State<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends State<SignupScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _emailFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // Initialize the auth provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AuthProvider>().initialize();
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    _emailFocusNode.dispose();
    super.dispose();
  }

  void _clearValidationMessage() {
    context.read<AuthProvider>().clearError();
  }

  Future<void> _handleSignup() async {
    await context.read<AuthProvider>().submitSignupForm(
          _emailController.text,
          _formKey,
          context,
        );
  }

  Future<void> _launchUrl(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Stack(
          children: [
            CustomScaffold(
              backgroundColor: Colors.transparent,
              appBar: const AuthHeader(),
              body: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(20),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            const SizedBox(height: 20),

                            // Title
                            const AuthTitle(title: 'Create Account'),

                            const SizedBox(height: 20),

                            // Email Field
                            AuthFormField(
                              controller: _emailController,
                              focusNode: _emailFocusNode,
                              hintText: 'Enter your email',
                              labelText: 'Email',
                              keyboardType: TextInputType.emailAddress,
                              isEnabled: authProvider.isFieldEnabled,
                              textCapitalization: TextCapitalization.none,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter your email';
                                }
                                if (!AuthValidators.validateEmail(value)) {
                                  return 'Please enter a valid email';
                                }
                                if (authProvider.errorMessage.isNotEmpty) {
                                  return authProvider.errorMessage;
                                }
                                return null;
                              },
                              autoValidateMode:
                                  AutovalidateMode.onUserInteraction,
                              onChanged: (_) => _clearValidationMessage(),
                            ),

                            const SizedBox(height: 10),

                            // Terms and Conditions Checkbox
                            TermsCheckbox(
                              isChecked: authProvider.agreeToPolicy,
                              onChanged: (value) {
                                authProvider.agreeToPolicy = value ?? false;
                              },
                              text: 'I agree to El Junto ',
                              secondLinkText: 'Terms of Service',
                              onSecondLinkTap: () => _launchUrl(
                                AppConstants.termsAndConditionUrl.toString(),
                              ),
                              secondText: ' and ',
                              linkText: 'Privacy Policy',
                              onLinkTap: () => _launchUrl(
                                AppConstants.privacyPolicyUrl.toString(),
                              ),
                              showValidation: !authProvider.agreeToPolicy,
                              validationMessage:
                                  '* Please agree to the Terms and Conditions',
                            ),

                            const SizedBox(height: 10),

                            // Age Confirmation Checkbox
                            TermsCheckbox(
                              isChecked: authProvider.is18YearsOld,
                              onChanged: (value) {
                                authProvider.is18YearsOld = value ?? false;
                              },
                              text: 'I am 18 years or older',
                              showValidation: !authProvider.is18YearsOld,
                              validationMessage:
                                  '* You must be 18 years or older to sign up',
                            ),

                            const SizedBox(height: 20),

                            // Signup Button
                            AppButton(
                              text: 'Sign Up',
                              height: 44,
                              borderRadius: 36,
                              onPressed: authProvider.isFieldEnabled
                                  ? _handleSignup
                                  : null,
                              isLoading: authProvider.signupLoading,
                              isEnabled: authProvider.isFieldEnabled,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // Contact Us
                  AuthFooter(),
                ],
              ),
            ),

            // No Connection Tag
            const NoConnectionTag(bottomPosition: 70),
          ],
        );
      },
    );
  }
}
