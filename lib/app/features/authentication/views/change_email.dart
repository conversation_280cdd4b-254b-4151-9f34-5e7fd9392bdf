import 'dart:developer';

import 'package:eljunto/app/core/widgets/app_button.dart';
import 'package:eljunto/app/core/widgets/configurable_popup.dart';
import 'package:eljunto/app/core/widgets/custom_scaffold.dart';
import 'package:eljunto/app/core/widgets/unified_app_bar.dart';
import 'package:eljunto/app/features/authentication/models/auth_models.dart';
import 'package:eljunto/app/features/authentication/providers/auth_provider.dart';
import 'package:eljunto/app/features/authentication/utils/auth_utils.dart';
import 'package:eljunto/app/features/authentication/views/widgets/auth_form_field.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

class ChangeEmail extends StatelessWidget {
  final String? currentEmail;

  const ChangeEmail({super.key, this.currentEmail});

  @override
  Widget build(BuildContext context) {
    TextEditingController currentEmailController = TextEditingController();
    TextEditingController newEmailController = TextEditingController();
    TextEditingController otpController = TextEditingController();
    final focusNode = FocusNode();
    currentEmailController.text = currentEmail!;
    final newEmailFormKey = GlobalKey<FormState>();
    final otpFormKey = GlobalKey<FormState>();

    void clearValidationMessage() {
      context.read<AuthProvider>().clearError();
    }

    // OTP popup
    void otpPopup() {
      showDialog(
        context: context,
        builder: (context) => Consumer<AuthProvider>(
          builder: (context, authProvider, child) => Form(
            key: otpFormKey,
            child: ConfigurablePopupDialog(
              bodyText1: 'Change Email',
              text1FontSize: 14,
              textArea: PopupField(
                focusNode: focusNode,
                controller: otpController,
                isRequired: true,
                isTextAreaForOtp: true,
                label: 'Enter verification code',
                maxLines: 1,
                validationMessage: authProvider.errorMessage,
              ),
              buttons: [
                PopupButton(
                  text: 'Next',
                  isLoading: authProvider.otpLoading,
                  onPressed: () {
                    authProvider
                        .verifyOtp(
                      newEmailController.text,
                      otpController.text,
                      false,
                      authProvider.currentToken,
                      context,
                      AuthOtpState.changeEmail,
                    )
                        .then((_) async {
                      log('error message: ${authProvider.errorMessage}');
                      if (authProvider.currentStep == AuthStep.changeEmail) {
                        await AuthHelpers.clearUserSession(context);
                      }
                    });
                  },
                ),
                PopupButton(
                  text: 'Cancel',
                  style: AppButtonStyle.outlined,
                  onPressed: () {
                    clearValidationMessage();
                    context.pop();
                  },
                ),
              ],
            ),
          ),
        ),
      );
    }

    // New email popup
    void newEmailPopup() {
      showDialog(
        context: context,
        builder: (context) =>
            Consumer<AuthProvider>(builder: (context, authProvider, _) {
          return Form(
            key: newEmailFormKey,
            child: ConfigurablePopupDialog(
              bodyText1: 'Change Email',
              text1FontSize: 14,
              textArea: PopupField(
                controller: newEmailController,
                isRequired: true,
                placeholder: 'Enter new email',
                label: 'Email:',
                maxLines: 1,
                validationMessage: authProvider.errorMessage,
              ),
              buttons: [
                PopupButton(
                  text: 'Next',
                  isLoading: authProvider.isLoading,
                  onPressed: () {
                    authProvider
                        .verifyEmail(
                      newEmailController.text,
                      newEmailFormKey,
                      context,
                    )
                        .then((_) {
                      if (authProvider.currentToken.isNotEmpty) {
                        context.pop();
                        otpPopup();
                      }
                    });
                  },
                ),
                PopupButton(
                  text: 'Cancel',
                  style: AppButtonStyle.outlined,
                  onPressed: () => context.pop(),
                ),
              ],
            ),
          );
        }),
      );
    }

    return CustomScaffold(
      appBar: UnifiedAppBar.main(
        title: 'Change Email',
        showBackButton: true,
        onLeadingTap: () => context.pop(),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          spacing: 20,
          children: [
            const SizedBox.shrink(),

            // Current email field
            AuthFormField(
              controller: currentEmailController,
              hintText: 'Enter your email',
              labelText: 'Current email',
              isEnabled: false,
            ),

            // Save button
            AppButton(
              text: 'Change email',
              height: 40,
              borderRadius: 36,
              onPressed: () => newEmailPopup(),
            ),
          ],
        ),
      ),
    );
  }
}
