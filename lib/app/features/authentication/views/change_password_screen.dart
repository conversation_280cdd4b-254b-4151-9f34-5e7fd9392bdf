import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/app/core/widgets/configurable_popup.dart';
import 'package:eljunto/app/core/widgets/custom_scaffold.dart';
import 'package:eljunto/app/core/widgets/unified_app_bar.dart';
import 'package:eljunto/app/features/authentication/providers/auth_provider.dart';
import 'package:eljunto/app/features/authentication/views/widgets/auth_button.dart';
import 'package:eljunto/app/features/authentication/views/widgets/auth_form_field.dart';
import 'package:eljunto/app/features/authentication/views/widgets/validation_display.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../../core/services/session_manager.dart';
import '../../../core/services/setup_locator.dart';
import '../../../core/widgets/app_button.dart';

class ChangePasswordScreen extends StatefulWidget {
  const ChangePasswordScreen({super.key});

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  final formKey = GlobalKey<FormState>();
  final currentPasswordController = TextEditingController();
  final newPasswordController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  final currentPasswordFocusNode = FocusNode();
  final newPasswordFocusNode = FocusNode();
  final confirmPasswordFocusNode = FocusNode();
  final sessionManager = locator<SessionManager>();

  void clearValidationMessage() {
    context.read<AuthProvider>().clearError();
    context.read<AuthProvider>().resetPasswordFlags();
  }

  void _onPasswordChanged() {
    final authProvider = context.read<AuthProvider>();
    final newPassword = newPasswordController.text;
    final confirmPassword = confirmPasswordController.text;

    authProvider.validatePassword(newPassword);
    if (confirmPassword.isNotEmpty) {
      authProvider.validateConfirmPassword(confirmPassword, newPassword);
    }
    setState(() {});
  }

  void clearForm() {
    clearValidationMessage();
    currentPasswordController.clear();
    newPasswordController.clear();
    confirmPasswordController.clear();
  }

  void passwordChangePopup(bool isChangeSuccessful) {
    showDialog(
      context: context,
      builder: (context) => Consumer<AuthProvider>(
        builder: (context, provider, _) => ConfigurablePopupDialog(
          bodyText1: 'Change Password',
          bodyText2: isChangeSuccessful
              ? 'Your password has been changed'
              : context.read<AuthProvider>().errorMessage,
          text1FontSize: 16,
          text2FontSize: 14,
          buttons: [
            PopupButton(
              text: 'OK',
              height: 40,
              borderRadius: 36,
              isLoading: provider.isLoading,
              onPressed: () async {
                if (isChangeSuccessful) {
                  await provider.logout(context);
                  return;
                }
                clearForm();
                context.pop();
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> handleChangePassword() async {
    if (!formKey.currentState!.validate()) return;

    final passwordChanged = await context.read<AuthProvider>().changePassword(
          sessionManager.userEmail!,
          currentPasswordController.text,
          newPasswordController.text,
          confirmPasswordController.text,
        );

    if (passwordChanged) {
      passwordChangePopup(true);
    } else {
      passwordChangePopup(false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      appBar: UnifiedAppBar.main(
        title: 'Change Password',
        showBackButton: true,
        onLeadingTap: () {
          clearForm();
          context.pop();
        },
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Consumer<AuthProvider>(
          builder: (context, authProvider, _) => Form(
            key: formKey,
            child: Column(
              children: [
                const SizedBox(height: 24),

                // Current password field
                AuthFormField(
                  controller: currentPasswordController,
                  focusNode: currentPasswordFocusNode,
                  hintText: 'Enter you password',
                  labelText: 'Current Password',
                  isPassword: true,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a password';
                    }
                    return null;
                  },
                  autoValidateMode: AutovalidateMode.onUserInteraction,
                ),

                // Forgot password
                Align(
                  alignment: Alignment.centerRight,
                  child: AuthTextButton(
                    text: 'Forgot Password?',
                    textStyle: lbItalic.copyWith(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      decoration: TextDecoration.underline,
                    ),
                    onPressed: authProvider.isFieldEnabled
                        ? () => context.pushNamed(
                              'set-password',
                              extra: {
                                "isForgotPassword": true,
                                "email": sessionManager.userEmail?.trim(),
                              },
                            )
                        : null,
                  ),
                ),

                // New password field
                AuthFormField(
                  controller: newPasswordController,
                  focusNode: newPasswordFocusNode,
                  hintText: 'Enter you password',
                  labelText: 'New Password',
                  isPassword: true,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a password';
                    }
                    return null;
                  },
                  autoValidateMode: AutovalidateMode.onUserInteraction,
                  onChanged: (value) => _onPasswordChanged(),
                ),

                // Password Strength Indicator
                PasswordStrengthIndicator(
                  isPasswordValid: authProvider.isPasswordValid,
                  isPasswordComplex: authProvider.isPasswordComplex,
                  isPasswordMatch: authProvider.isPasswordMatch,
                  showValidation: newPasswordController.text.isNotEmpty,
                  showMatchValidation:
                      confirmPasswordController.text.isNotEmpty,
                ),

                const SizedBox(height: 20),

                // Confirm Password Field
                AuthFormField(
                  controller: confirmPasswordController,
                  focusNode: confirmPasswordFocusNode,
                  hintText: 'Confirm your password',
                  labelText: 'Confirm Password',
                  isPassword: true,
                  isEnabled: authProvider.isFieldEnabled,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please confirm your password';
                    }
                    if (value != confirmPasswordController.text) {
                      return 'Passwords do not match';
                    }
                    if (authProvider.errorMessage.isNotEmpty) {
                      return authProvider.errorMessage;
                    }
                    return null;
                  },
                  onChanged: (value) => _onPasswordChanged(),
                ),

                const SizedBox(height: 25),

                // Set Password Button
                AppButton(
                  text: 'Done',
                  height: 40,
                  borderRadius: 36,
                  onPressed:
                      authProvider.isFieldEnabled ? handleChangePassword : null,
                  isLoading: authProvider.passwordLoading,
                  isEnabled: authProvider.isFieldEnabled,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
