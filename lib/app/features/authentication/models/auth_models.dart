// Authentication Models
// Consolidated authentication data models following established patterns

class LoginRequest {
  final String email;
  final String password;
  final String fcmToken;
  final String deviceId;

  LoginRequest({
    required this.email,
    required this.password,
    required this.fcmToken,
    required this.deviceId,
  });

  Map<String, dynamic> toJson() {
    return {
      "userEmailId": email,
      "userCred": password,
      "fcmToken": fcmToken,
      "deviceId": deviceId,
    };
  }
}

class LoginResponse {
  final String? userName;
  final String? userEmailId;
  final dynamic lastLogoutTime;
  final String? token;
  final int? userId;
  final List<RoleAction>? roleActions;
  final String? portalType;
  final bool? isUserNameAvailable;
  final String? userHandle;
  final bool? isUserBioAndLocationAvailable;
  final int? userCreatedDate;
  final dynamic subscriptionId;
  final dynamic subscriptionName;
  final dynamic subscriptionStatus;
  final dynamic userBillingPurchaseTime;
  final dynamic userBillingStartDate;
  final dynamic userBillingEndDate;

  LoginResponse({
    this.userName,
    this.userEmailId,
    this.lastLogoutTime,
    this.token,
    this.userId,
    this.roleActions,
    this.portalType,
    this.isUserNameAvailable,
    this.userHandle,
    this.isUserBioAndLocationAvailable,
    this.userCreatedDate,
    this.subscriptionId,
    this.subscriptionName,
    this.subscriptionStatus,
    this.userBillingPurchaseTime,
    this.userBillingStartDate,
    this.userBillingEndDate,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) => LoginResponse(
        userName: json["userName"],
        userEmailId: json["userEmailId"],
        lastLogoutTime: json["lastLogoutTime"],
        token: json["token"],
        userId: json["userId"],
        roleActions: json["roleActions"] == null
            ? []
            : List<RoleAction>.from(
                json["roleActions"]!.map((x) => RoleAction.fromJson(x))),
        portalType: json["portalType"],
        isUserNameAvailable: json["isUserNameAvailable"],
        userHandle: json["userHandle"],
        isUserBioAndLocationAvailable: json["isUserBioAndLocationAvailable"],
        userCreatedDate: json["userCreatedDate"],
        subscriptionId: json["subscriptionId"],
        subscriptionName: json["subscriptionName"],
        subscriptionStatus: json["subscriptionStatus"],
        userBillingPurchaseTime: json["userBillingPurchaseTime"],
        userBillingStartDate: json["userBillingStartDate"],
        userBillingEndDate: json["userBillingEndDate"],
      );

  Map<String, dynamic> toJson() => {
        "userName": userName,
        "userEmailId": userEmailId,
        "lastLogoutTime": lastLogoutTime,
        "token": token,
        "userId": userId,
        "roleActions": roleActions == null
            ? []
            : List<dynamic>.from(roleActions!.map((x) => x.toJson())),
        "portalType": portalType,
        "isUserNameAvailable": isUserNameAvailable,
        "userHandle": userHandle,
        "isUserBioAndLocationAvailable": isUserBioAndLocationAvailable,
        "userCreatedDate": userCreatedDate,
        "subscriptionId": subscriptionId,
        "subscriptionName": subscriptionName,
        "subscriptionStatus": subscriptionStatus,
        "userBillingPurchaseTime": userBillingPurchaseTime,
        "userBillingStartDate": userBillingStartDate,
        "userBillingEndDate": userBillingEndDate,
      };
}

class RoleAction {
  final String? portalType;
  final dynamic route;
  final int? roleId;
  final String? name;
  final String? roleName;
  final dynamic icon;
  final int? id;
  final bool? menu;

  RoleAction({
    this.portalType,
    this.route,
    this.roleId,
    this.name,
    this.roleName,
    this.icon,
    this.id,
    this.menu,
  });

  factory RoleAction.fromJson(Map<String, dynamic> json) => RoleAction(
        portalType: json["portalType"],
        route: json["route"],
        roleId: json["roleId"],
        name: json["name"],
        roleName: json["roleName"],
        icon: json["icon"],
        id: json["id"],
        menu: json["menu"],
      );

  Map<String, dynamic> toJson() => {
        "portalType": portalType,
        "route": route,
        "roleId": roleId,
        "name": name,
        "roleName": roleName,
        "icon": icon,
        "id": id,
        "menu": menu,
      };
}

class SignupRequest {
  final String email;

  SignupRequest({required this.email});

  Map<String, dynamic> toJson() {
    return {'userEmailId': email};
  }
}

class SignupResponse {
  final bool success;
  final int statusCode;
  final String? token;
  final String? message;

  SignupResponse({
    required this.success,
    required this.statusCode,
    this.token,
    this.message,
  });

  factory SignupResponse.fromJson(Map<String, dynamic> json) {
    return SignupResponse(
      success: json['statusCode'] == 200,
      statusCode: json['statusCode'] ?? 0,
      token: json['data']?['token'],
      message: json['message'],
    );
  }
}

class OtpRequest {
  final String email;
  final String token;
  final String otp;
  final bool isForgotPassword;

  OtpRequest({
    required this.email,
    required this.token,
    required this.otp,
    required this.isForgotPassword,
  });

  Map<String, dynamic> toJson() {
    return {
      'userEmailId': email,
      'token': token,
      'passcode': otp,
      'flag': isForgotPassword,
    };
  }
}

class OtpResponse {
  final bool success;
  final int statusCode;
  final String? message;
  final Map<String, dynamic>? data;

  OtpResponse({
    required this.success,
    required this.statusCode,
    this.message,
    this.data,
  });

  factory OtpResponse.fromJson(Map<String, dynamic> json) {
    return OtpResponse(
      success: json['statusCode'] == 200,
      statusCode: json['statusCode'] ?? 0,
      message: json['message'],
      data: json['data'],
    );
  }
}

class PasswordSetupRequest {
  final String email;
  final String token;
  final String otp;
  final String password;
  final String fcmToken;
  final String deviceId;

  PasswordSetupRequest({
    required this.email,
    required this.token,
    required this.otp,
    required this.password,
    required this.fcmToken,
    required this.deviceId,
  });

  Map<String, dynamic> toJson() {
    return {
      'userEmailId': email,
      'token': token,
      'passcode': otp,
      'userCred': password,
      'fcmToken': fcmToken,
      'deviceId': deviceId,
    };
  }
}

class PasswordResetRequest {
  final String email;
  final String token;
  final String otp;
  final String password;

  PasswordResetRequest({
    required this.email,
    required this.token,
    required this.otp,
    required this.password,
  });

  Map<String, dynamic> toJson() {
    return {
      'userEmailId': email,
      'tokenCred': otp,
      'token': token,
      'userCred': password,
      'confirmUserCred': password,
    };
  }
}

class ChangePasswordRequest {
  final String userEmail;
  final String currentPassword;
  final String newPassword;
  final String confirmPassword;

  ChangePasswordRequest({
    required this.userEmail,
    required this.currentPassword,
    required this.newPassword,
    required this.confirmPassword,
  });

  Map<String, dynamic> toJson() {
    return {
      "userEmailId": userEmail,
      "currentCred": currentPassword,
      "userCred": newPassword,
      "confirmUserCred": confirmPassword
    };
  }
}

class ProfileSetupRequest {
  final String email;
  final String token;
  final String otp;
  final String userName;
  final String userHandle;
  final String userLocation;
  final String userBio;

  ProfileSetupRequest({
    required this.email,
    required this.token,
    required this.otp,
    required this.userName,
    required this.userHandle,
    required this.userLocation,
    required this.userBio,
  });

  Map<String, dynamic> toJson() {
    return {
      'userEmailId': email,
      'token': token,
      'passcode': otp,
      'userName': userName,
      'userHandle': userHandle,
      'userLocation': userLocation,
      'userBio': userBio,
    };
  }
}

class HandleAvailabilityRequest {
  final String email;
  final String userHandle;

  HandleAvailabilityRequest({
    required this.email,
    required this.userHandle,
  });

  Map<String, dynamic> toJson() {
    return {
      'userEmailId': email,
      'userHandle': userHandle,
    };
  }
}

class AuthResponse {
  final bool success;
  final int statusCode;
  final String? message;
  final Map<String, dynamic>? data;

  AuthResponse({
    required this.success,
    required this.statusCode,
    this.message,
    this.data,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      success: json['statusCode'] == 200,
      statusCode: json['statusCode'] ?? 0,
      message: json['message'],
      data: json['data'],
    );
  }
}

// Authentication state models
enum AuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

enum AuthStep {
  login,
  signup,
  otpVerification,
  passwordSetup,
  profileSetup,
  completed,
  changeEmail,
}

class AuthData {
  final String? token;
  final String? email;
  final String? fcmToken;
  final String? deviceId;
  final Map<String, dynamic>? userData;

  AuthData({
    this.token,
    this.email,
    this.fcmToken,
    this.deviceId,
    this.userData,
  });

  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'email': email,
      'fcmToken': fcmToken,
      'deviceId': deviceId,
      'userData': userData,
    };
  }

  factory AuthData.fromJson(Map<String, dynamic> json) {
    return AuthData(
      token: json['token'],
      email: json['email'],
      fcmToken: json['fcmToken'],
      deviceId: json['deviceId'],
      userData: json['userData'],
    );
  }
}

enum AuthAction { none, logout, delete }

enum AuthOtpState { none, firstSetup, forgotPassword, changeEmail }
