import 'dart:async';
import 'dart:developer';

import 'package:dio/dio.dart' as dio;
import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/network/http/http_service.dart';
import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/app/features/authentication/models/auth_models.dart';
import 'package:eljunto/app/features/authentication/utils/auth_utils.dart';

/// Consolidated Authentication Service
/// Handles all authentication-related API calls, validation, and storage operations
class AuthService {
  final String _baseURL = ApiConstants.flavorBaseUrl;
  final HttpApiService _apiService = locator<HttpApiService>();
  final SessionManager _sessionManager = SessionManager();

  // API Methods

  /// Login user with email and password
  Future<LoginResponse> login(LoginRequest request) async {
    try {
      final requestJson = request.toJson();
      log("Login Payload: $requestJson");

      dio.Response response = await _apiService.post(
        '$_baseURL/auth/signin',
        requestJson,
      );

      if (response.data != null) {
        log('Login Response 111: ${response.data}');
        final loginResponse = LoginResponse.fromJson(response.data);
        log('Login Response 222: ${loginResponse.toJson()}');
        if (loginResponse.token != null) {
          // Save authentication data locally
          await _sessionManager.createSession(
            userId: loginResponse.userId!,
            token: loginResponse.token!,
            userEmail: loginResponse.userEmailId!,
            userName: loginResponse.userName!,
            userHandle: loginResponse.userHandle!,
          );
        }
        return loginResponse;
      } else {
        return LoginResponse();
      }
    } catch (e) {
      log("Login error: $e");
      return LoginResponse();
    }
  }

  /// Verify email for signup
  Future<SignupResponse> verifyEmail(SignupRequest request) async {
    try {
      log("Email verification payload: ${request.toJson()}");

      dio.Response response = await _apiService.post(
        '$_baseURL/auth/email-verification',
        request.toJson(),
      );

      return SignupResponse.fromJson(response.data);
    } catch (e) {
      log("Email verification error: $e");
      return SignupResponse(
        success: false,
        statusCode: 0,
        message: 'Something went wrong',
      );
    }
  }

  /// Verify OTP
  Future<OtpResponse> verifyOtp(OtpRequest request) async {
    try {
      log("OTP verification payload: ${request.toJson()}");

      dio.Response response = await _apiService.post(
        '$_baseURL/auth/pass-code-verify',
        request.toJson(),
      );

      return OtpResponse.fromJson(response.data);
    } catch (e) {
      log("OTP verification error: $e");
      return OtpResponse(
        success: false,
        statusCode: 0,
        message: 'Something went wrong',
      );
    }
  }

  /// Set password for new user
  Future<AuthResponse> setPassword(PasswordSetupRequest request) async {
    try {
      // Encrypt password before sending
      final encryptedRequest = PasswordSetupRequest(
        email: request.email,
        token: request.token,
        otp: request.otp,
        password: AuthHelpers.encryptPassword(request.password),
        fcmToken: request.fcmToken,
        deviceId: request.deviceId,
      );

      log("Set password payload: ${encryptedRequest.toJson()}");

      dio.Response response = await _apiService.post(
        '$_baseURL/auth/sign-up-set-password',
        encryptedRequest.toJson(),
      );

      final authResponse = AuthResponse.fromJson(response.data);

      if (authResponse.success && authResponse.data != null) {
        // Save authentication data locally
        final loginResponse = LoginResponse.fromJson(authResponse.data!);
        await _sessionManager.createSession(
          userId: loginResponse.userId,
          token: loginResponse.token ?? '',
          userEmail: loginResponse.userEmailId ?? '',
          userName: loginResponse.userName ?? '',
          userHandle: loginResponse.userHandle ?? '',
        );
      }

      return authResponse;
    } catch (e) {
      log("Set password error: $e");
      return AuthResponse(
        success: false,
        statusCode: 0,
        message: 'Something went wrong',
      );
    }
  }

  /// Reset password
  Future<AuthResponse> resetPassword(PasswordResetRequest request) async {
    try {
      // Encrypt password before sending
      final encryptedRequest = PasswordResetRequest(
        email: request.email,
        token: request.token,
        otp: request.otp,
        password: AuthHelpers.encryptPassword(request.password),
      );

      log("Reset password payload: ${encryptedRequest.toJson()}");

      dio.Response response = await _apiService.post(
        '$_baseURL/auth/reset-password',
        encryptedRequest.toJson(),
      );

      return AuthResponse.fromJson(response.data);
    } catch (e) {
      log("Reset password error: $e");
      return AuthResponse(
        success: false,
        statusCode: 0,
        message: 'Something went wrong',
      );
    }
  }

  /// Change password
  Future<AuthResponse> changePassword(ChangePasswordRequest request) async {
    dio.Response response = await _apiService.post(
      '$_baseURL/auth/change-password',
      request.toJson(),
    );

    return AuthResponse.fromJson(response.data);
  }

  /// Set user name and handle
  Future<AuthResponse> setNameAndHandle(ProfileSetupRequest request) async {
    try {
      log("Profile setup payload: ${request.toJson()}");

      dio.Response response = await _apiService.post(
        '$_baseURL/auth/signup-name-handle',
        request.toJson(),
      );

      return AuthResponse.fromJson(response.data);
    } catch (e) {
      log("Profile setup error: $e");
      return AuthResponse(
        success: false,
        statusCode: 0,
        message: 'Something went wrong',
      );
    }
  }

  /// Check handle availability
  Future<AuthResponse> checkHandleAvailability(
      HandleAvailabilityRequest request) async {
    try {
      log("Handle availability payload: ${request.toJson()}");

      dio.Response response = await _apiService.post(
        '$_baseURL/auth/check-available-handle',
        request.toJson(),
      );

      return AuthResponse.fromJson(response.data);
    } catch (e) {
      log("Handle availability error: $e");
      return AuthResponse(
        success: false,
        statusCode: 0,
        message: 'Something went wrong',
      );
    }
  }

  Future<dio.Response> logout() async {
    final email = _sessionManager.userEmail;
    return _apiService.get('$_baseURL/auth/signout/$email');
  }

  Future<dio.Response> deleteAccount() async {
    final userId = _sessionManager.userId;
    return _apiService.delete('$_baseURL/users/delete-user/$userId');
  }
}
