// To parse this JSON data, do
//
//     final applePurchaseResponseModel = applePurchaseResponseModelFromJson(jsonString);
//     final subscriptionModel = subscriptionModelFromJson(jsonString);
//     final subscriptionResponseModel = subscriptionResponseModelFromJson(jsonString);
//     final verifySubscriptionModel = verifySubscriptionModelFromJson(jsonString);

import 'dart:convert';

ApplePurchaseResponseModel applePurchaseResponseModelFromJson(String str) =>
    ApplePurchaseResponseModel.fromJson(json.decode(str));

String applePurchaseResponseModelToJson(ApplePurchaseResponseModel data) =>
    json.encode(data.toJson());

class ApplePurchaseResponseModel {
  final String? productId;
  final String? purchaseId;
  final String? localVerificationData;
  final String? serverVerificationData;
  final String? purchaseStatus;
  final int? transactionDate;
  final String? source;

  ApplePurchaseResponseModel({
    this.productId,
    this.purchaseId,
    this.localVerificationData,
    this.serverVerificationData,
    this.purchaseStatus,
    this.transactionDate,
    this.source,
  });

  factory ApplePurchaseResponseModel.fromJson(Map<String, dynamic> json) =>
      ApplePurchaseResponseModel(
        productId: json["productId"],
        purchaseId: json["purchaseId"],
        localVerificationData: json["localVerificationData"],
        serverVerificationData: json["serverVerificationData"],
        purchaseStatus: json["purchaseStatus"],
        transactionDate: json["transactionDate"],
        source: json["source"],
      );

  Map<String, dynamic> toJson() => {
        "productId": productId,
        "purchaseId": purchaseId,
        "localVerificationData": localVerificationData,
        "serverVerificationData": serverVerificationData,
        "purchaseStatus": purchaseStatus,
        "transactionDate": transactionDate,
        "source": source,
      };
}

SubscriptionModel subscriptionModelFromJson(String str) =>
    SubscriptionModel.fromJson(json.decode(str));

String subscriptionModelToJson(SubscriptionModel data) =>
    json.encode(data.toJson());

class SubscriptionModel {
  final String? message;
  final int? statusCode;
  final SubscriptionInfo? data;

  SubscriptionModel({
    this.message,
    this.statusCode,
    this.data,
  });

  factory SubscriptionModel.fromJson(Map<String, dynamic> json) =>
      SubscriptionModel(
        message: json["message"],
        statusCode: json["statusCode"],
        data: json["data"] == null
            ? null
            : SubscriptionInfo.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "statusCode": statusCode,
        "data": data?.toJson(),
      };
}

class SubscriptionInfo {
  final List<SubscriptionDetail>? subscriptionDetails;
  final bool? isFreeTrial;

  SubscriptionInfo({
    this.subscriptionDetails,
    this.isFreeTrial,
  });

  factory SubscriptionInfo.fromJson(Map<String, dynamic> json) =>
      SubscriptionInfo(
        subscriptionDetails: json["subscriptionDetails"] == null
            ? []
            : List<SubscriptionDetail>.from(json["subscriptionDetails"]!
                .map((x) => SubscriptionDetail.fromJson(x))),
        isFreeTrial: json["isFreeTrial"],
      );

  Map<String, dynamic> toJson() => {
        "subscriptionDetails": subscriptionDetails == null
            ? []
            : List<dynamic>.from(subscriptionDetails!.map((x) => x.toJson())),
        "isFreeTrial": isFreeTrial,
      };
}

class SubscriptionDetail {
  final int? subscriptionId;
  final String? productId;
  final String? subscriptionBillingCycle;
  final String? subscriptionName;
  final String? subscriptionDescription;
  final double? subscriptionPrice;
  final String? subscriptionCurrency;
  final int? subscriptionStatus;
  final int? subscriptionCreatedAt;

  SubscriptionDetail({
    this.subscriptionId,
    this.productId,
    this.subscriptionBillingCycle,
    this.subscriptionName,
    this.subscriptionDescription,
    this.subscriptionPrice,
    this.subscriptionCurrency,
    this.subscriptionStatus,
    this.subscriptionCreatedAt,
  });

  factory SubscriptionDetail.fromJson(Map<String, dynamic> json) =>
      SubscriptionDetail(
        subscriptionId: json["subscriptionId"],
        productId: json["productId"],
        subscriptionBillingCycle: json["subscriptionBillingCycle"],
        subscriptionName: json["subscriptionName"],
        subscriptionDescription: json["subscriptionDescription"],
        subscriptionPrice: json["subscriptionPrice"]?.toDouble(),
        subscriptionCurrency: json["subscriptionCurrency"],
        subscriptionStatus: json["subscriptionStatus"],
        subscriptionCreatedAt: json["subscriptionCreatedAt"],
      );

  Map<String, dynamic> toJson() => {
        "subscriptionId": subscriptionId,
        "productId": productId,
        "subscriptionBillingCycle": subscriptionBillingCycle,
        "subscriptionName": subscriptionName,
        "subscriptionDescription": subscriptionDescription,
        "subscriptionPrice": subscriptionPrice,
        "subscriptionCurrency": subscriptionCurrency,
        "subscriptionStatus": subscriptionStatus,
        "subscriptionCreatedAt": subscriptionCreatedAt,
      };
}

SubscriptionResponseModel subscriptionResponseModelFromJson(String str) =>
    SubscriptionResponseModel.fromJson(json.decode(str));

String subscriptionResponseModelToJson(SubscriptionResponseModel data) =>
    json.encode(data.toJson());

class SubscriptionResponseModel {
  final String? purchasedId;
  final int? subscriptionId;
  final int? transactionDate;
  final String? verificationSource;
  final String? verificationStatus;
  final String? productId;
  final bool? verificationPendingCompletePurchase;
  final dynamic verificationError;
  final LocalVerificationData? localVerificationData;

  SubscriptionResponseModel({
    this.purchasedId,
    this.subscriptionId,
    this.transactionDate,
    this.verificationSource,
    this.verificationStatus,
    this.productId,
    this.verificationPendingCompletePurchase,
    this.verificationError,
    this.localVerificationData,
  });

  SubscriptionResponseModel copyWith({
    String? purchasedId,
    int? subscriptionId,
    int? transactionDate,
    String? verificationSource,
    String? verificationStatus,
    String? productId,
    bool? verificationPendingCompletePurchase,
    dynamic verificationError,
    LocalVerificationData? localVerificationData,
  }) =>
      SubscriptionResponseModel(
        purchasedId: purchasedId ?? this.purchasedId,
        subscriptionId: subscriptionId ?? this.subscriptionId,
        transactionDate: transactionDate ?? this.transactionDate,
        verificationSource: verificationSource ?? this.verificationSource,
        verificationStatus: verificationStatus ?? this.verificationStatus,
        productId: productId ?? this.productId,
        verificationPendingCompletePurchase:
            verificationPendingCompletePurchase ??
                this.verificationPendingCompletePurchase,
        verificationError: verificationError ?? this.verificationError,
        localVerificationData:
            localVerificationData ?? this.localVerificationData,
      );

  factory SubscriptionResponseModel.fromJson(Map<String, dynamic> json) =>
      SubscriptionResponseModel(
        purchasedId: json["purchasedId"],
        subscriptionId: json["subscriptionId"],
        transactionDate: json["transactionDate"],
        verificationSource: json["verificationSource"],
        verificationStatus: json["verificationStatus"],
        productId: json["productId"],
        verificationPendingCompletePurchase:
            json["VerificationPendingCompletePurchase"],
        verificationError: json["Verification error"],
        localVerificationData: json["localVerificationData"] == null
            ? null
            : LocalVerificationData.fromJson(json["localVerificationData"]),
      );

  Map<String, dynamic> toJson() => {
        "purchasedId": purchasedId,
        "subscriptionId": subscriptionId,
        "transactionDate": transactionDate,
        "verificationSource": verificationSource,
        "verificationStatus": verificationStatus,
        "productId": productId,
        "VerificationPendingCompletePurchase":
            verificationPendingCompletePurchase,
        "Verification error": verificationError,
        "localVerificationData": localVerificationData?.toJson(),
      };
}

class LocalVerificationData {
  final String? orderId;
  final int? purchaseTime;
  final int? purchaseState;
  final String? purchaseToken;
  final double? purchaseAmount;
  final String? purchaseCode;
  final String? packageName;
  final String? productId;
  final int? quantity;
  final bool? autoRenewing;
  final bool? acknowledged;

  LocalVerificationData({
    this.orderId,
    this.purchaseTime,
    this.purchaseState,
    this.purchaseToken,
    this.purchaseAmount,
    this.purchaseCode,
    this.packageName,
    this.productId,
    this.quantity,
    this.autoRenewing,
    this.acknowledged,
  });

  LocalVerificationData copyWith({
    String? orderId,
    int? purchaseTime,
    int? purchaseState,
    String? purchaseToken,
    double? purchaseAmount,
    String? purchaseCode,
    String? packageName,
    String? productId,
    int? quantity,
    bool? autoRenewing,
    bool? acknowledged,
  }) =>
      LocalVerificationData(
        orderId: orderId ?? this.orderId,
        purchaseTime: purchaseTime ?? this.purchaseTime,
        purchaseState: purchaseState ?? this.purchaseState,
        purchaseToken: purchaseToken ?? this.purchaseToken,
        purchaseAmount: purchaseAmount ?? this.purchaseAmount,
        purchaseCode: purchaseCode ?? this.purchaseCode,
        packageName: packageName ?? this.packageName,
        productId: productId ?? this.productId,
        quantity: quantity ?? this.quantity,
        autoRenewing: autoRenewing ?? this.autoRenewing,
        acknowledged: acknowledged ?? this.acknowledged,
      );

  factory LocalVerificationData.fromJson(Map<String, dynamic> json) =>
      LocalVerificationData(
        orderId: json["orderId"],
        purchaseTime: json["purchaseTime"],
        purchaseState: json["purchaseState"],
        purchaseToken: json["purchaseToken"],
        purchaseAmount: json["purchaseAmount"]?.toDouble(),
        purchaseCode: json["purchaseCode"],
        packageName: json["packageName"],
        productId: json["productId"],
        quantity: json["quantity"],
        autoRenewing: json["autoRenewing"],
        acknowledged: json["acknowledged"],
      );

  Map<String, dynamic> toJson() => {
        "orderId": orderId,
        "purchaseTime": purchaseTime,
        "purchaseState": purchaseState,
        "purchaseToken": purchaseToken,
        "purchaseAmount": purchaseAmount,
        "purchaseCode": purchaseCode,
        "packageName": packageName,
        "productId": productId,
        "quantity": quantity,
        "autoRenewing": autoRenewing,
        "acknowledged": acknowledged,
      };
}

VerifySubscriptionModel verifySubscriptionModelFromJson(String str) =>
    VerifySubscriptionModel.fromJson(json.decode(str));

String verifySubscriptionModelToJson(VerifySubscriptionModel data) =>
    json.encode(data.toJson());

class VerifySubscriptionModel {
  final String? message;
  final int? statusCode;
  final VerifyData? data;

  VerifySubscriptionModel({
    this.message,
    this.statusCode,
    this.data,
  });

  factory VerifySubscriptionModel.fromJson(Map<String, dynamic> json) =>
      VerifySubscriptionModel(
        message: json["message"],
        statusCode: json["statusCode"],
        data: json["data"] == null ? null : VerifyData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "statusCode": statusCode,
        "data": data?.toJson(),
      };
}

class VerifyData {
  final int? subscriptionId;
  final int? userId;
  final double? ubAmount;
  final String? ubCurrency;
  final int? ubStartDate;
  final int? ubEndDate;
  final int? ubCreatedAt;
  final int? ubPurchaseTime;
  final String? usubStatus;
  final String? usubProductId;
  final String? ubPlatform;

  VerifyData({
    this.subscriptionId,
    this.userId,
    this.ubAmount,
    this.ubCurrency,
    this.ubStartDate,
    this.ubEndDate,
    this.ubCreatedAt,
    this.ubPurchaseTime,
    this.usubStatus,
    this.usubProductId,
    this.ubPlatform,
  });

  factory VerifyData.fromJson(Map<String, dynamic> json) => VerifyData(
        subscriptionId: json["subscriptionId"],
        userId: json["userId"],
        ubAmount: json["ubAmount"],
        ubCurrency: json["ubCurrency"],
        ubStartDate: json["ubStartDate"],
        ubEndDate: json["ubEndDate"],
        ubCreatedAt: json["ubCreatedAt"],
        ubPurchaseTime: json["ubPurchaseTime"],
        usubStatus: json["usubStatus"],
        usubProductId: json["usubProductId"],
        ubPlatform: json["ubPlatform"],
      );

  Map<String, dynamic> toJson() => {
        "subscriptionId": subscriptionId,
        "userId": userId,
        "ubAmount": ubAmount,
        "ubCurrency": ubCurrency,
        "ubStartDate": ubStartDate,
        "ubEndDate": ubEndDate,
        "ubCreatedAt": ubCreatedAt,
        "ubPurchaseTime": ubPurchaseTime,
        "usubStatus": usubStatus,
        "usubProductId": usubProductId,
        "ubPlatform": ubPlatform,
      };
}
