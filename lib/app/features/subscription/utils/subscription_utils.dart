import 'dart:developer';

import 'package:in_app_purchase/in_app_purchase.dart';

/// Utility class containing helper functions for subscription feature
class SubscriptionUtils {
  /// Filters products to exclude free products for display
  static List<ProductDetails> getDisplayProducts(
      List<ProductDetails> products) {
    return products
        .where((product) => product.price.toLowerCase() != 'free')
        .toList();
  }

  /// Filters and extracts free trial products
  static List<ProductDetails> getFreeTrialProducts(
      List<ProductDetails> products) {
    return products
        .where((product) => product.price.toLowerCase() == 'free')
        .toList();
  }

  /// Filters products based on purchased product IDs
  static List<ProductDetails> filterPurchasedProducts(
    List<ProductDetails> products,
    List<String> purchasedProductIds,
  ) {
    return products.where((product) {
      bool hasFreeTrial = product.price.toLowerCase() == 'free';

      if (purchasedProductIds.contains(product.id)) {
        return !hasFreeTrial; // Remove free trial if already subscribed
      }
      return true;
    }).toList();
  }

  /// Determines the correct product to use for purchase based on free trial status
  static ProductDetails? getProductForPurchase(
    ProductDetails selectedProduct,
    bool isFreeTrial,
    List<ProductDetails> freeProducts,
  ) {
    if (!isFreeTrial || freeProducts.isEmpty) {
      return selectedProduct;
    }

    // Find matching free trial product
    try {
      return freeProducts.firstWhere(
        (freeProduct) => freeProduct.id == selectedProduct.id,
        orElse: () => selectedProduct,
      );
    } catch (e) {
      log('Error finding free trial product: $e');
      return selectedProduct;
    }
  }

  /// Logs product selection for debugging
  static void logProductSelection(ProductDetails? product, String context) {
    if (product != null) {
      log('$context - Selected Product: ${product.id}, Price: ${product.price}');
    } else {
      log('$context - No product selected');
    }
  }

  /// Validates if a product can be purchased
  static bool canPurchaseProduct(ProductDetails? product) {
    return product != null;
  }

  /// Gets formatted price display for a product
  static String getFormattedPrice(ProductDetails product) {
    return product.price;
  }

  /// Gets formatted title display for a product
  static String getFormattedTitle(ProductDetails product) {
    return product.title;
  }

  /// Checks if product is selected
  static bool isProductSelected(
      ProductDetails product, String? selectedProductId) {
    return selectedProductId == product.id;
  }

  /// Handles automatic product selection for single product scenarios
  static ProductDetails? handleSingleProductAutoSelection(
    List<ProductDetails> displayProducts,
    bool isFreeTrial,
    List<ProductDetails> freeProducts,
  ) {
    if (displayProducts.length != 1) {
      return null;
    }

    final product = displayProducts.first;
    return getProductForPurchase(product, isFreeTrial, freeProducts);
  }
}
