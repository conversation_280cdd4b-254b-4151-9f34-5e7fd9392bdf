import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/enums/enums.dart';
import 'package:eljunto/app/core/widgets/app_button.dart';
import 'package:eljunto/app/core/widgets/custom_scaffold.dart';
import 'package:eljunto/app/core/widgets/unified_app_bar.dart';
import 'package:eljunto/app/features/subscription/providers/subscription_provider.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../core/utils/text_style.dart';
import '../models/subscription_models.dart';

class ManageSubscriptionScreen extends StatefulWidget {
  const ManageSubscriptionScreen({super.key});

  @override
  State createState() => _ManageSubscriptionScreenState();
}

class _ManageSubscriptionScreenState extends State<ManageSubscriptionScreen> {
  String? subscriptionName;
  double? subscriptionValue;
  String? subscriptionCurrency;
  String currencySymbol = '';
  String? purchasePlatform;
  SubscriptionDetail? subscriptionDetail;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider =
          Provider.of<SubscriptionProvider>(context, listen: false);
      provider.getSubscriptionDetails().then((_) {
        subscriptionDetail =
            provider.subscriptionDetails?.data?.subscriptionDetails?.first;
        setState(() {});
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final subscriptionProvider = Provider.of<SubscriptionProvider>(context);
    return CustomScaffold(
      appBar: UnifiedAppBar.main(
        title: 'Manage Subscription',
        showBackButton: true,
        onLeadingTap: () => context.pop(),
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Skeletonizer(
          enabled: subscriptionProvider.isLoading,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 20,
            children: [
              Text(
                "Current Plan",
                style: lbRegular.copyWith(
                  fontSize: 18,
                  color: AppConstants.primaryColor,
                ),
              ),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(23),
                  border: Border.all(
                    color: AppConstants.primaryColor,
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      subscriptionDetail?.subscriptionName ?? '',
                      overflow: TextOverflow.ellipsis,
                      style: lbRegular.copyWith(
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Text(
                          '${subscriptionDetail?.subscriptionCurrency} ${subscriptionDetail?.subscriptionPrice}',
                          style: lbBold.copyWith(
                            fontSize: 20,
                            color: AppConstants.primaryColor,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    AppButton(
                      text: 'Cancel Subscription',
                      height: 40,
                      borderRadius: 36,
                      onPressed: () async {
                        String cancellationUrl = '';
                        if (purchasePlatform == PlatformPurchase.google.value) {
                          cancellationUrl =
                              PlatformSubscriptionLink.googlePlayLink.value;
                        } else if (purchasePlatform ==
                            PlatformPurchase.apple.value) {
                          cancellationUrl =
                              PlatformSubscriptionLink.appStoreLink.value;
                        }
                        await launchUrl(
                          Uri.parse(cancellationUrl),
                          mode: LaunchMode.externalApplication,
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
