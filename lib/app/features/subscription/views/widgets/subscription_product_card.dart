import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/app/core/widgets/app_button.dart';
import 'package:eljunto/app/features/subscription/providers/subscription_provider.dart';
import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:provider/provider.dart';

/// Clean, stateless widget for displaying subscription products
/// Focuses only on UI rendering, business logic handled by provider
class SubscriptionProductCard extends StatelessWidget {
  final List<ProductDetails>? subProducts;

  const SubscriptionProductCard({
    super.key,
    this.subProducts,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<SubscriptionProvider>(
      builder: (context, provider, child) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Column(
            children: [
              _buildProductList(provider, context),
            ],
          ),
        );
      },
    );
  }

  /// Builds the list of subscription products
  Widget _buildProductList(
      SubscriptionProvider provider, BuildContext context) {
    return Column(
      children: provider.displayProduct.map((product) {
        return _buildProductCard(product, provider, context);
      }).toList(),
    );
  }

  /// Builds individual product card
  Widget _buildProductCard(
    ProductDetails product,
    SubscriptionProvider provider,
    BuildContext context,
  ) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.white.withValues(alpha: .6)),
        borderRadius: BorderRadius.circular(24),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        // To make the card "hug" its content
        children: [
          ListTile(
            contentPadding: EdgeInsets.zero,
            title: Text(
              product.title,
              style: lbRegular.copyWith(
                fontSize: 14,
                color: AppConstants.primaryColor,
              ),
            ),
            subtitle: Text(
              product.price,
              style: lbBold.copyWith(
                fontSize: 20,
                color: AppConstants.primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            trailing: Transform.scale(
              scale: 1.4,
              child: Radio(
                fillColor: WidgetStatePropertyAll(
                  AppConstants.primaryColor,
                ),
                value: product.id,
                groupValue: provider.selectedProductId,
                onChanged: null,
              ),
            ),
            onTap: () => provider.displayProduct.length <= 1
                ? null
                : provider.toggleProductSelection(product),
          ),
          const SizedBox(height: 20),
          // The trial button
          AppButton(
            text: provider.isFreeTrial ? 'Start Free Trial' : 'Subscribe',
            height: 40,
            borderRadius: 36,
            style: AppButtonStyle.solid,
            isEnabled: provider.selectedProductId != null,
            onPressed: () => provider.initiatePurchase(),
          ),
        ],
      ),
    );
  }
}
