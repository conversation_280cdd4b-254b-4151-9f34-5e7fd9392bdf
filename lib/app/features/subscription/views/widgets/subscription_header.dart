import 'dart:ui';

import 'package:eljunto/app/features/subscription/providers/subscription_provider.dart';
import 'package:eljunto/app/features/subscription/views/widgets/subscription_product_card.dart';
import 'package:flutter/material.dart';

import '../../../../core/constants.dart';
import '../../../../core/utils/text_style.dart';

class SubscriptionHeader extends StatelessWidget {
  final SubscriptionProvider subscriptionProvider;

  const SubscriptionHeader({
    super.key,
    required this.subscriptionProvider,
  });

  @override
  Widget build(BuildContext context) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 8, sigmaY: 16),
      child: Container(
        height: 400,
        width: MediaQuery.sizeOf(context).width,
        padding: EdgeInsets.only(top: kToolbarHeight + 20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppConstants.textGreenColor,
              Color(0XFFDDE7E3),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          boxShadow: [
            BoxShadow(
              color: const Color.fromRGBO(0, 0, 0, 0.25),
              offset: const Offset(0, 4),
              blurRadius: 24,
              spreadRadius: 0,
            ),
          ],
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(44),
            bottomRight: Radius.circular(44),
          ),
          border: Border.all(color: Colors.white.withValues(alpha: 0.6)),
        ),
        child: Column(
          // spacing: 24,
          children: [
            Image.asset(
              AppConstants.elJuntoLogo,
              height: 80,
              width: 80,
              fit: BoxFit.contain,
              filterQuality: FilterQuality.high,
            ),
            const SizedBox(height: 20),
            Text(
              subscriptionProvider.isFreeTrial
                  ? 'This is the final step!'
                  : 'Free trial ended',
              style: lbBold.copyWith(fontSize: 20),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            SubscriptionProductCard(
              subProducts: subscriptionProvider.products,
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
