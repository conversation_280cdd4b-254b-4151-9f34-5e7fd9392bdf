import 'package:eljunto/app/core/constants.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../core/widgets/app_button.dart';
import '../../../authentication/models/auth_models.dart';
import '../../../authentication/providers/auth_provider.dart';

class AccountActions extends StatelessWidget {
  const AccountActions({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        final isLoading = authProvider.isLoading;
        final loadingAction = authProvider.accountAction;

        if (isLoading) {
          return Center(
            child: AppButton(
              text: loadingAction == AuthAction.logout
                  ? 'Logout'
                  : 'Delete Account',
              height: 40,
              borderRadius: 36,
              style: loadingAction == AuthAction.logout
                  ? AppButtonStyle.outlined
                  : AppButtonStyle.destructive,
              textColor: loadingAction == AuthAction.logout
                  ? AppConstants.primaryColor
                  : null,
              destructiveColor: AppConstants.redColor,
              isLoading: true,
              onPressed: () {},
            ),
          );
        }

        return Row(
          children: [
            Expanded(
              child: AppButton(
                text: 'Logout',
                height: 40,
                borderRadius: 36,
                style: AppButtonStyle.outlined,
                textColor: AppConstants.primaryColor,
                onPressed: () async => await authProvider.logout(context),
              ),
            ),
            const SizedBox(width: 10),
            Expanded(
              child: AppButton(
                text: 'Delete Account',
                height: 40,
                borderRadius: 36,
                style: AppButtonStyle.destructive,
                destructiveColor: AppConstants.redColor,
                onPressed: () {},
              ),
            ),
          ],
        );
      },
    );
  }
}
