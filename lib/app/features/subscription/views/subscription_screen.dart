import 'dart:io';

import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/app/core/widgets/custom_scaffold.dart';
import 'package:eljunto/app/features/subscription/providers/subscription_provider.dart';
import 'package:eljunto/app/features/subscription/views/widgets/account_actions.dart';
import 'package:eljunto/app/features/subscription/views/widgets/subscription_header.dart';
import 'package:eljunto/reusableWidgets/connection_error/no_connection_tag.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../core/constants.dart';
import '../../authentication/views/widgets/auth_footer.dart';

class SubscriptionScreen extends StatefulWidget {
  const SubscriptionScreen({super.key});

  @override
  State createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends State<SubscriptionScreen> {
  bool isLogout = false;

  @override
  void initState() {
    super.initState();
    _initializeSubscription();
  }

  /// Initialize subscription data and listeners
  void _initializeSubscription() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider =
          Provider.of<SubscriptionProvider>(context, listen: false);
      provider.fetchProducts().then((_) {
        // Initialize subscription options after products are fetched
        provider.showSubscriptionOptions(context);
      });
      provider.purchaseStream.listen((purchaseDetailsList) {
        if (mounted) {
          provider.handlePurchaseUpdates(purchaseDetailsList, context);
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      body: Consumer<SubscriptionProvider>(
        builder: (context, provider, child) {
          return Skeletonizer(
            effect: const SoldColorEffect(
              color: AppConstants.skeletonforgroundColor,
              lowerBound: 0.1,
              upperBound: 0.5,
            ),
            containersColor: AppConstants.skeletonBackgroundColor,
            enabled: provider.isLoading,
            child: Stack(
              children: [
                Column(
                  children: [
                    SubscriptionHeader(subscriptionProvider: provider),
                    Expanded(
                      child: SingleChildScrollView(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              if (provider.isSubscribedClicked &&
                                  provider.purchaseProduct == null)
                                Text(
                                  'Please select a subscription to continue.',
                                  style: lbRegular.copyWith(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w300,
                                    color: AppConstants.redColor,
                                  ),
                                )
                              else
                                const SizedBox.shrink(),
                              const SizedBox(height: 20),
                              _buildSubscriptionPoints(
                                provider.isFreeTrial
                                    ? 'Start your 30-Day Free Trial, full access for 30 days.'
                                    : Platform.isAndroid
                                        ? 'To continue enjoying full access, please click above to subscribe through the play store.'
                                        : 'To continue enjoying full access, please click above to subscribe through the app store.',
                              ),
                              const SizedBox(height: 10),
                              _buildSubscriptionPoints(
                                  'Only subscribers have unlimited access to all features.'),
                              const SizedBox(height: 10),
                              _buildSubscriptionPoints(
                                  'Start or join a book club with integrated videoconferencing and messaging.'),
                              const SizedBox(height: 10),
                              _buildSubscriptionPoints(
                                  'Search to see who is into your favorite books or author.'),
                              const SizedBox(height: 10),
                              _buildSubscriptionPoints(
                                  'Customer support for any app feedback or technical issues.'),
                              const SizedBox(height: 10),
                              _buildSubscriptionPoints(
                                  'Customer support to add books to the database.'),
                              const SizedBox(height: 10),
                              _buildPP(),
                              const SizedBox(height: 20),
                              if (provider.displayProduct.isNotEmpty) ...[
                                Text(
                                  "${provider.planPrice} per month after 30-day free trial. Cancel anytime. Subscription auto-renews unless canceled at least 24 hours before the end of the current period.",
                                  style: lbRegular.copyWith(fontSize: 14),
                                ),
                                const SizedBox(height: 25),
                              ],
                              AccountActions(),
                              const SizedBox(height: 5),
                            ],
                          ),
                        ),
                      ),
                    ),

                    // Contact Us
                    const AuthFooter(),
                  ],
                ),
                if (provider.isPurchaseLoading)
                  const Align(
                    alignment: Alignment.center,
                    child: CircularProgressIndicator(
                      color: AppConstants.primaryColor,
                      strokeWidth: 4,
                      strokeAlign: 2,
                    ),
                  ),
                NoConnectionTag(bottomPosition: 75),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildPP() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Padding(
          padding: EdgeInsets.only(top: 10.0),
          child: Icon(Icons.circle, color: Colors.black, size: 8),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 3.0),
            child: Text.rich(
              TextSpan(
                text: 'View our ',
                style: lbRegular.copyWith(fontSize: 14),
                children: [
                  TextSpan(
                    text: 'privacy policy',
                    style: lbRegular.copyWith(
                      fontSize: 14,
                      color: AppConstants.blueColor,
                      decoration: TextDecoration.underline,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () => launchUrl(
                            AppConstants.privacyPolicyUrl,
                          ),
                  ),
                  TextSpan(
                    text: ' and ',
                    style: lbRegular.copyWith(fontSize: 14),
                  ),
                  TextSpan(
                    text: 'terms of service',
                    style: lbRegular.copyWith(
                      fontSize: 14,
                      color: AppConstants.blueColor,
                      decoration: TextDecoration.underline,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () => launchUrl(
                            AppConstants.termsAndConditionUrl,
                          ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSubscriptionPoints(String pointText) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Padding(
          padding: EdgeInsets.only(top: 12.0),
          child: Icon(Icons.circle, color: Colors.black, size: 8),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 3.0),
            child: Text(
              pointText,
              style: lbRegular.copyWith(fontSize: 14),
            ),
          ),
        ),
      ],
    );
  }
}
