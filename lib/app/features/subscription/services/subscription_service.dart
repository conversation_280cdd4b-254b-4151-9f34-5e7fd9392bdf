import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart' as dio;
import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/network/http/http_service.dart';
import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_android/in_app_purchase_android.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:in_app_purchase_storekit/store_kit_2_wrappers.dart';

import '../models/subscription_models.dart';

class SubscriptionService extends ChangeNotifier {
  final String baseURL = ApiConstants.flavorBaseUrl;
  final _apiService = locator<HttpApiService>();
  final _sessionManager = locator<SessionManager>();

  SubscriptionModel? subscriptionDetails;
  bool isFreeTrial = false;
  VerifySubscriptionModel? verifySubscriptionModel;
  List<ProductDetails> products = [];
  InAppPurchase inAppPurchase = InAppPurchase.instance;
  late StreamSubscription<List<PurchaseDetails>> streamSubscription;
  ProductDetails? purchaseProduct;
  bool isPurchaseLoading = false;
  String planPrice = '';
  int subscriptionId = 0;
  bool isPurchaseApp = false;
  bool isApplePurchaseApp = false;
  List<String> _subscriptionDetailList = [];

  List<String> get subscriptionDetailList => _subscriptionDetailList;
  bool _verifySubscription = true;

  bool get verifySubscription => _verifySubscription;

  Future<dio.Response> getSubscriptionDetails(String osType) async {
    return _apiService
        .get('$baseURL/user-subscription/user/check-validity?osType=$osType');
  }

  Future<void> fetchSubscriptionDetails(BuildContext context) async {
    String osType = Platform.isAndroid ? 'google' : 'apple';
    try {
      final response = await getSubscriptionDetails(osType);
      log("Subscription Controller Details 12121212 : ${response.data}");
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData =
            response.data is String ? jsonDecode(response.data) : response.data;

        subscriptionDetails = SubscriptionModel.fromJson(jsonData);
        isFreeTrial = subscriptionDetails?.data?.isFreeTrial ?? false;
        getSubscriptionDetailList();
        log("Subscription Ids Details : ${subscriptionDetails?.data}");
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await _sessionManager.userLogout(context, payload);
        }
        notifyListeners();
      } else {
        subscriptionDetails = null;
        log("Subscription else Details : ${response.data}");
      }
    } catch (e) {
      log("Subscription : ${e.toString()}");
    }
  }

  Future<dio.Response> isActiveSubscription() async {
    return _apiService.get('$baseURL/user-subscription/check-subscription');
  }

  Future<bool> checkActiveSubscription() async {
    try {
      final response = await isActiveSubscription();
      log("Is Active Subscription Details : ${response.data}");
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData =
            response.data is String ? jsonDecode(response.data) : response.data;
        log('decode data: $jsonData');
        verifySubscriptionModel = VerifySubscriptionModel.fromJson(jsonData);
        log("Is Active Subscription Details : ${verifySubscriptionModel?.toJson()}");
        final subStatus = verifySubscriptionModel?.data?.usubStatus;
        notifyListeners();
        return subStatus == 'ACTIVE';
      } else {
        verifySubscriptionModel = null;
        return false;
      }
    } catch (e) {
      verifySubscriptionModel = null;
      log("Verify Subscription : ${e.toString()}");
      return false;
    }
  }

  Future<dio.Response> googlePlaypurchaseResponse(
      SubscriptionResponseModel obj) async {
    Map<String, dynamic> payload = {
      "purchasedId": obj.purchasedId,
      "subscriptionId": obj.subscriptionId,
      "transactionDate": obj.transactionDate,
      "verificationSource": obj.verificationSource,
      "verificationStatus": obj.verificationStatus,
      "productId": obj.productId,
      "VerificationPendingCompletePurchase":
          obj.verificationPendingCompletePurchase,
      "Verification error": obj.verificationError,
      "localVerificationData": obj.localVerificationData?.copyWith(
        acknowledged: obj.localVerificationData?.acknowledged,
        autoRenewing: obj.localVerificationData?.autoRenewing,
        orderId: obj.localVerificationData?.orderId,
        purchaseToken: obj.localVerificationData?.purchaseToken,
        purchaseAmount: obj.localVerificationData?.purchaseAmount,
        purchaseCode: obj.localVerificationData?.purchaseCode,
        purchaseState: obj.localVerificationData?.purchaseState,
        purchaseTime: obj.localVerificationData?.purchaseTime,
        packageName: obj.localVerificationData?.packageName,
        productId: obj.localVerificationData?.productId,
        quantity: obj.localVerificationData?.quantity,
      ),
    };
    log("Purchase Plan Object : $payload");
    return _apiService.post('$baseURL/billing-subscription/create', payload);
  }

  Future<bool> purchaseSubscription(SubscriptionResponseModel payload) async {
    try {
      final response = await googlePlaypurchaseResponse(payload);
      log("Purchase Subscription : ${response.data}");
      if (response.statusCode == 200) {
        isPurchaseApp = true;
        log("Subscription Success : ${response.data}");
      } else {
        isPurchaseApp = false;
        log("Subscription Details : ${response.data}");
      }
    } catch (e) {
      isPurchaseApp = false;
      log("Purchase Subscription : ${e.toString()}");
    }
    return isPurchaseApp;
  }

  Future<dio.Response> appStorePurchaseResponse(
      ApplePurchaseResponseModel obj) async {
    Map<String, dynamic> payload = {
      "productId": obj.productId,
      "purchaseId": obj.purchaseId,
      "localVerificationData": obj.localVerificationData,
      "serverVerificationData": obj.serverVerificationData,
      "purchaseStatus": obj.purchaseStatus,
      "transactionDate": obj.transactionDate,
      "source": obj.source,
    };
    log("Purchase Plan Object : $payload");
    return _apiService.post(
        '$baseURL/billing-subscription/apple/create', payload);
  }

  Future<bool> applePurchaseSubscription(
      ApplePurchaseResponseModel payload) async {
    try {
      final response = await appStorePurchaseResponse(payload);
      log("Purchase Subscription : ${response.data}");
      if (response.statusCode == 200) {
        isApplePurchaseApp = true;
        log("Subscription Success : ${response.data}");
      } else {
        isApplePurchaseApp = false;
        log("Subscription Details : ${response.data}");
      }
    } catch (e) {
      isApplePurchaseApp = false;
      log("Purchase Subscription : ${e.toString()}");
    }
    return isApplePurchaseApp;
  }

  Future<void> fetchProducts(BuildContext context) async {
    if (!await inAppPurchase.isAvailable()) {
      log("[IAP] Store not available.");
      products = [];
      notifyListeners();
      return;
    }

    log("[IAP] Store is available. Fetching products...");

    try {
      final productIds = subscriptionDetailList.toSet();
      if (productIds.isEmpty) throw "No product IDs available";

      final response =
          await InAppPurchase.instance.queryProductDetails(productIds);
      if (response.error != null) throw response.error!;
      if (response.productDetails.isEmpty) {
        throw "No products found. Invalid IDs or setup issue.";
      }

      log("[IAP] Products found: ${response.productDetails.map((p) => p.id).toList()}");
      final purchasedProductIds = await getUserPurchasedProducts();
      log("[IAP] User previously purchased IDs: $purchasedProductIds");

      products = response.productDetails.where((product) {
        final isPurchased = purchasedProductIds.contains(product.id);
        bool isFreeTrial = false;

        if (Platform.isIOS) {
          if (product is AppStoreProductDetails) {
            final iosProduct = product;
            isFreeTrial = iosProduct.skProduct.introductoryPrice?.price == '0';
          } else if (product is AppStoreProduct2Details) {
            final iosProduct = product;
            final subscription = iosProduct.sk2Product.subscription;
            try {
              final subscriptionOffer =
                  subscription?.promotionalOffers.firstWhere(
                (offer) => offer.type == SK2SubscriptionOfferType.introductory,
              );
              isFreeTrial = subscriptionOffer?.type ==
                  SK2SubscriptionOfferType.introductory;
            } on StateError {
              isFreeTrial = false;
            }
          }
          log("[IAP] iOS product ${product.id}: Free Trial=$isFreeTrial, Purchased=$isPurchased");
        } else if (Platform.isAndroid) {
          final androidProduct = product as GooglePlayProductDetails;
          isFreeTrial = androidProduct.productDetails.subscriptionOfferDetails
                  ?.any((offer) => offer.pricingPhases
                      .any((phase) => phase.priceAmountMicros == 0)) ??
              false;
          log("[IAP] Android product ${product.id}: Free Trial=$isFreeTrial, Purchased=$isPurchased");
        }
        planPrice = product.price;
        notifyListeners();

        return !(isFreeTrial && isPurchased);
      }).toList();
      log("[IAP] Filtered products: ${products.map((p) => p.id).toList()}");
    } catch (e) {
      log("[IAP] Error: $e");
      products = [];
    }

    notifyListeners();
  }

  Future<List<String>> getUserPurchasedProducts() async {
    return [];
  }

  Future<void> handlePurchaseUpdates(
      List<PurchaseDetails> purchaseDetailsList, BuildContext context) async {
    for (var purchaseDetails in purchaseDetailsList) {
      if (purchaseDetails.status == PurchaseStatus.pending) {
        log("Pending: ${purchaseDetails.productID}");
      } else {
        if (purchaseDetails.status == PurchaseStatus.error) {
          log("Error: ${purchaseDetails.productID}");
        } else if (purchaseDetails.status == PurchaseStatus.purchased ||
            purchaseDetails.status == PurchaseStatus.restored) {
          await subscriptionPayloadFunction(purchaseDetails, context);
        }
      }
      if (purchaseDetails.pendingCompletePurchase) {
        inAppPurchase.completePurchase(purchaseDetails);
      }
    }
  }

  Future<void> subscriptionPayloadFunction(
      PurchaseDetails purchaseDetails, BuildContext context) async {
    isPurchaseLoading = true;
    notifyListeners();
    if (Platform.isAndroid) {
      final data = subscriptionDetails?.data?.subscriptionDetails;

      for (var element in data ?? <SubscriptionDetail>[]) {
        if (element.productId == purchaseDetails.productID) {
          subscriptionId = element.subscriptionId ?? 0;
          log("Subscription Match Id : ${element.subscriptionId}");
        }
      }

      final subscription =
          jsonDecode(purchaseDetails.verificationData.localVerificationData);

      final payload = SubscriptionResponseModel(
        productId: purchaseDetails.productID,
        purchasedId: purchaseDetails.purchaseID,
        transactionDate: int.parse(purchaseDetails.transactionDate ?? ''),
        subscriptionId: subscriptionId,
        verificationSource: purchaseDetails.verificationData.source,
        verificationStatus: purchaseDetails.status.toString(),
        verificationPendingCompletePurchase:
            purchaseDetails.pendingCompletePurchase,
        verificationError: purchaseDetails.error,
        localVerificationData: LocalVerificationData(
          acknowledged: subscription['acknowledged'],
          orderId: subscription['orderId'],
          packageName: subscription['packageName'],
          productId: subscription['productId'],
          purchaseState: subscription['purchaseState'],
          purchaseTime: subscription['purchaseTime'],
          quantity: subscription['quantity'],
          purchaseToken: subscription['purchaseToken'],
          autoRenewing: subscription['autoRenewing'],
        ),
      );

      final success = await purchaseSubscription(payload);
      if (success) {
        log('Subscription Success');
        await checkActiveSubscription();
      } else {
        log('Subscription Failed');
      }
    } else {
      final payload = ApplePurchaseResponseModel(
        localVerificationData:
            purchaseDetails.verificationData.localVerificationData,
        productId: purchaseDetails.productID,
        purchaseId: purchaseDetails.purchaseID,
        purchaseStatus: purchaseDetails.status.toString(),
        serverVerificationData:
            purchaseDetails.verificationData.serverVerificationData,
        source: purchaseDetails.verificationData.source,
        transactionDate: int.parse(purchaseDetails.transactionDate ?? ''),
      );

      final success = await applePurchaseSubscription(payload);
      if (success) {
        log('Subscription Success');
        await checkActiveSubscription();
      } else {
        log('Subscription Failed');
      }
    }
    isPurchaseLoading = false;
    notifyListeners();
  }

  Future<void> buyProduct(ProductDetails product) async {
    final PurchaseParam purchaseParam = PurchaseParam(
      productDetails: product,
    );
    await inAppPurchase.buyNonConsumable(
      purchaseParam: purchaseParam,
    );
  }

  Future<void> getSubscriptionDetailList() async {
    _subscriptionDetailList = subscriptionDetails?.data?.subscriptionDetails
            ?.map((e) => e.productId ?? '')
            .toList() ??
        [];
    notifyListeners();
  }

  Future<void> updateApiTriggerValue(bool value) async {
    _verifySubscription = value;
    notifyListeners();
  }
}
