import 'dart:developer';

import 'package:eljunto/app/core/providers/app_version_provider.dart';
import 'package:eljunto/app/core/providers/connectivity_provider.dart';
import 'package:eljunto/app/core/routes/redirect_logic.dart';
import 'package:eljunto/app/core/routes/route_persistence.dart';
import 'package:eljunto/app/core/services/deep_link_service.dart';
import 'package:eljunto/app/core/services/notification_service.dart';
import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/controller/subscription_controller.dart';
import 'package:eljunto/controller/user_credential_controller.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:provider/provider.dart';

class SplashProvider extends ChangeNotifier {
  final _provider = locator<ConnectivityProvider>();

  // This single method now orchestrates the entire startup sequence.
  Future<void> initialize(BuildContext context) async {
    // Listen for connectivity changes. This was previously in initState.
    _provider.statusStream.listen((status) {
      log("Connection listener : $status");
      if (status == InternetStatus.connected) {
        // When connection is established, run the main initialization.
        _initializeAppLogic(context);
      } else {
        // If connection is lost, navigate to the no-network screen.
        context.go('/full-no-network');
      }
    });
  }

  // Contains the core logic from the original initializeApp method.
  Future<void> _initializeAppLogic(BuildContext context) async {
    final versionProvider =
        Provider.of<AppVersionProvider>(context, listen: false);
    final sessionManager = locator<SessionManager>();
    final subscriptionController =
        Provider.of<SubscriptionController>(context, listen: false);

    try {
      // For tracking app lifecycle to distinguish background vs fresh start
      final isBackgroundResume = AppRedirect.appInitialized;

      // Get pending deep link but don't clear it yet
      String? pendingIntentDeepLink =
          await RoutePersistence.getPendingRouteWithoutClearing();

      bool isFromNotificationClick = false;
      if (RoutePersistence.lastClickedDeepLink != null) {
        pendingIntentDeepLink = RoutePersistence.lastClickedDeepLink;
        RoutePersistence.lastClickedDeepLink = null; // Clear this flag
        isFromNotificationClick = true;
      }

      // Perform parallel initialization tasks
      await Future.wait(
        [
          versionProvider.refreshVersionInfo(),
          NotificationServices().checkLoginStatusAndInit(context),
        ],
      );

      final isLoggedIn = sessionManager.isLoggedIn;
      final userName = sessionManager.userName;
      final userEmailId = sessionManager.userEmail;

      bool isSubscriptionActive = false;
      if (isLoggedIn) {
        await subscriptionController.isActiveSubscription();
        isSubscriptionActive = subscriptionController
                    .verifySubscriptionModel?.data?.usubStatus ==
                'ACTIVE' ||
            subscriptionController.verifySubscriptionModel?.data?.usubStatus ==
                'CANCELLED';
      }

      Uri? systemDeepLink;
      if (pendingIntentDeepLink == null) {
        systemDeepLink = await DeepLinkService().consumePendingDeepLink();
        if (systemDeepLink != null) {
          pendingIntentDeepLink =
              _parseRouteFromDeepLink(context, systemDeepLink);
        }
      }

      await Future.delayed(const Duration(milliseconds: 1250));

      AppRedirect.appInitialized = true;

      if (!Navigator.of(context).mounted) return;

      if (isBackgroundResume && !isFromNotificationClick) {
        await RoutePersistence.clearPendingRoute();
        if (isLoggedIn && userName != null) {
          context.go('/Home');
          return;
        }
      }

      if (!Navigator.of(context).mounted) return;

      if (!isLoggedIn) {
        context.go('/login');
      } else if (userName == null) {
        context.go('/set-name-handle', extra: {'email': userEmailId});
      } else if (!isSubscriptionActive) {
        context.go('/subscription');
      } else if (pendingIntentDeepLink != null) {
        context.go(pendingIntentDeepLink);
        await RoutePersistence.clearPendingRoute();
      } else {
        context.go('/Home');
      }
    } catch (e) {
      debugPrint("Error during initialization: $e");
      AppRedirect.appInitialized = true;
      await Future.delayed(const Duration(milliseconds: 500));
      if (Navigator.of(context).mounted) {
        context.go('/login'); // Safe fallback
      }
    }
  }

  // Helper method for parsing deep links, now part of the controller.
  String _parseRouteFromDeepLink(BuildContext context, Uri deepLink) {
    final token = Provider.of<UserCredentialController>(context, listen: false)
        .userCredential
        .jwttoken;

    if (token == null || token.isEmpty) {
      DeepLinkService().storePendingDeepLink(deepLink);
      return '/login';
    }

    String? clubId = deepLink.queryParameters['bookClubId'];
    String? userId = deepLink.queryParameters['userId'];

    if (deepLink.path.contains('ManageIncomeRequest')) {
      return '/Clubs/user-club-details/clubsScreen4/ManageIncomeRequest?bookClubId=${clubId ?? ''}&userId=${userId ?? ''}';
    } else if (deepLink.path.contains('user-club-details')) {
      return '/Clubs/user-club-details?bookClubId=${clubId ?? ''}&userId=${userId ?? ''}';
    } else if (deepLink.path.contains('club-invitations')) {
      return '/Clubs/club-invitations?userId=${userId ?? ''}';
    } else if (deepLink.path.contains('chat-screen')) {
      return '/chat-screen?userId=${userId ?? ''}&bookClubId=${clubId ?? ''}';
    }

    return deepLink.toString();
  }
}
