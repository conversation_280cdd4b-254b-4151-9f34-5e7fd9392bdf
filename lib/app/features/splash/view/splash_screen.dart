import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/app_config.dart';
import 'package:eljunto/app/core/widgets/version_display.dart';
import 'package:eljunto/app/features/splash/providers/splash_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    // Use a post-frame callback to ensure the context is available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Access the controller and kick off the initialization
      Provider.of<SplashProvider>(context, listen: false).initialize(context);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConfig.shared.flavor == Flavor.dev
          ? Colors.white
          : AppConstants.textGreenColor,
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Image.asset(
            'assets/images/eljunto_logo_dark_teal.png',
            height: 100,
            width: 80,
            filterQuality: FilterQuality.high,
            fit: BoxFit.contain,
          ),
          const SizedBox(
            height: 10,
          ),
          const Align(
            alignment: Alignment.center,
            child: VersionDisplay(),
          ),
        ],
      ),
    );
  }
}
