import 'dart:developer';

import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/network/http/http_service.dart';
import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:flutter/material.dart';

import '../model/settings_model.dart';

class SettingsService {
  final _sessionManager = locator<SessionManager>();
  final _apiService = locator<HttpApiService>();
  final _baseUrl = ApiConstants.flavorBaseUrl;

  // Add error message tracking like UserController
  String _errorMessage = '';

  String get errorMessage => _errorMessage;

  Future<List<NotificationSetting>> getNotificationSettings(
      BuildContext context) async {
    final userId = _sessionManager.userId;
    if (userId == null) {
      log('User not logged in');
      _errorMessage = 'User not logged in';
      return [];
    }

    try {
      final response = await _apiService
          .get('$_baseUrl/notifications/get-notifications?userId=$userId');

      if (response.statusCode == 200) {
        _errorMessage = ''; // Clear any previous errors
        return _parseNotificationSettings(response.data);
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await _sessionManager.userLogout(context, payload);
        }
        _errorMessage = 'Session expired';
        return [];
      } else {
        _errorMessage =
            response.data['message'] ?? 'Failed to fetch notification settings';
        log('Error fetching notification settings: ${response.data}');
        return [];
      }
    } catch (e) {
      _errorMessage = 'Network error occurred';
      log('Error fetching notification settings: $e');
      return [];
    }
  }

  List<NotificationSetting> _parseNotificationSettings(
      Map<String, dynamic> response) {
    final Map<int, String> notificationTypeTitles = {
      1: 'New Club Meeting',
      2: 'Meeting Time Change',
      3: 'Club Meeting Reminders',
      4: 'New Invitations',
      5: 'Club Requests\n(For Club Leaders)',
      6: 'New Club Messages',
    };
    final notificationSettings =
        (response['data'] as List).map<NotificationSetting>((item) {
      final type = item['notificationType'] as int;
      final title = notificationTypeTitles[type] ?? 'Unknown Notification';
      final email = item['emailNotification'] == 1;
      final os = item['osNotification'] == 1;

      return NotificationSetting(
        title,
        email,
        os,
        true,
        true,
      );
    }).toList();
    log('Parsed notification settings: ${notificationSettings.map((e) => e.toJson()).toList()}');

    return notificationSettings;
  }

  Future<bool> updateNotificationSetting(
      BuildContext context, Map<String, dynamic> payload) async {
    final success = await controlNotification(payload);
    return success;
  }

  // Updated to match UserController pattern with proper error handling
  Future<bool> controlNotification(Map<String, dynamic> payload) async {
    bool isNotificationValueUpdate = false;
    try {
      final response = await _apiService.post(
          "$_baseUrl/notifications/control-notification", payload);

      if (response.statusCode == 200) {
        isNotificationValueUpdate = true;
        _errorMessage = ''; // Clear any previous errors
      } else if (response.statusCode == 401) {
        _errorMessage = 'Session expired';
        isNotificationValueUpdate = false;
      } else {
        _errorMessage =
            response.data['message'] ?? 'Failed to update notification setting';
        isNotificationValueUpdate = false;
      }
    } catch (e) {
      _errorMessage = 'Network error occurred';
      log(e.toString());
      isNotificationValueUpdate = false;
    }
    return isNotificationValueUpdate;
  }

  Future<void> logout(BuildContext context) async {
    final userMailId = _sessionManager.userEmail;
    if (userMailId != null) {
      await _sessionManager.userLogout(context, {});
    }
  }
}
