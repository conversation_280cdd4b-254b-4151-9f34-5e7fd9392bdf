import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/app/core/widgets/custom_scaffold.dart';
import 'package:eljunto/app/core/widgets/unified_app_bar.dart';
import 'package:eljunto/app/features/settings/views/widgets/notification_settings_row.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../model/settings_model.dart';
import '../providers/settings_provider.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() =>
      _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState
    extends State<NotificationSettingsScreen> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) async => await context
        .read<SettingsProvider>()
        .getNotificationSettings(context));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final settingsProvider =
        Provider.of<SettingsProvider>(context, listen: true);

    return CustomScaffold(
      appBar: UnifiedAppBar.main(
        title: 'Notification Settings',
        showBackButton: true,
        onLeadingTap: () => context.pop(),
      ),
      body: Skeletonizer(
        enabled: settingsProvider.isLoading,
        effect: const SoldColorEffect(
          color: AppConstants.skeletonforgroundColor,
          lowerBound: 0.1,
          upperBound: 0.5,
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Column(
            spacing: 10,
            children: [
              const SizedBox(height: 15),
              _buildHeader(),
              Expanded(
                child: settingsProvider.isLoading
                    ? _buildLoadingList()
                    : settingsProvider.notificationSettings.isEmpty
                        ? _buildEmptyState()
                        : _buildSettingsList(settingsProvider),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 35.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Text('Email', style: lbRegular.copyWith(fontSize: 14)),
          const SizedBox(width: 55),
          Text('OS', style: lbRegular.copyWith(fontSize: 14)),
        ],
      ),
    );
  }

  Widget _buildLoadingList() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      spacing: 20,
      children: List.generate(
        5,
        (index) {
          final tempSetting =
              NotificationSetting('Loading...', false, false, true, true);
          return NotificationSettingRow(setting: tempSetting);
        },
      ),
    );
    return ListView.builder(
      itemCount: 5,
      itemBuilder: (context, index) {
        // Create a temporary setting for loading state
        final tempSetting =
            NotificationSetting('Loading...', false, false, true, true);
        return NotificationSettingRow(setting: tempSetting);
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No notification settings found',
            style: lbRegular.copyWith(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Please try refreshing the page',
            style: lbRegular.copyWith(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsList(SettingsProvider settingsProvider) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      spacing: 20,
      children: settingsProvider.notificationSettings
          .map((item) => NotificationSettingRow(setting: item))
          .toList(),
    );
  }
}
