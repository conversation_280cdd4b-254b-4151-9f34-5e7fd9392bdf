import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/app/features/settings/providers/settings_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_switch/flutter_advanced_switch.dart';
import 'package:provider/provider.dart';

import '../../../../core/constants.dart';
import '../../../../core/utils/text_style.dart';
import '../../model/settings_model.dart';

// Centralized notification type mapping
class NotificationTypeMapping {
  static const Map<int, String> titles = {
    1: 'New Club Meeting',
    2: 'Meeting Time Change',
    3: 'Club Meeting Reminders',
    4: 'New Invitations',
    5: 'Club Requests\n(For Club Leaders)',
    6: 'New Club Messages',
  };

  static int getKey(String title) {
    return titles.entries
        .firstWhere(
          (entry) => entry.value == title,
          orElse: () => const MapEntry(-1, ''),
        )
        .key;
  }
}

class NotificationSettingRow extends StatelessWidget {
  final NotificationSetting setting;

  const NotificationSettingRow({
    super.key,
    required this.setting,
  });

  @override
  Widget build(BuildContext context) {
    final provider = Provider.of<SettingsProvider>(context, listen: false);
    final sessionManager = locator<SessionManager>();

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: AppConstants.primaryColor),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        spacing: 20,
        children: [
          Expanded(
            child: Text(
              setting.title,
              style: lbRegular.copyWith(fontSize: 14),
            ),
          ),
          if (setting.showEmailController &&
              setting.title != 'New Club Messages')
            _buildToggle(
              context,
              setting.emailController,
              (value) async {
                final userId = sessionManager.userId;
                if (userId == null) {
                  _showErrorDialog(context, 'User not logged in');
                  return;
                }

                final previousValue = setting.emailController.value;
                setting.emailController.value = value;

                final payload = {
                  "userId": userId,
                  "notificationTypeKey":
                      NotificationTypeMapping.getKey(setting.title),
                  "emailNotification": value ? 1 : 0,
                  "osNotification": setting.osController.value ? 1 : 0,
                };

                final success =
                    await provider.updateNotificationSetting(context, payload);

                if (!success && context.mounted) {
                  // Rollback on failure
                  setting.emailController.value = previousValue;
                  _showErrorDialog(context, provider.errorMessage);
                }
              },
            ),
          _buildToggle(
            context,
            setting.osController,
            (value) async {
              final userId = sessionManager.userId;
              if (userId == null) {
                _showErrorDialog(context, 'User not logged in');
                return;
              }

              final previousValue = setting.osController.value;
              setting.osController.value = value;

              final payload = {
                "userId": userId,
                "notificationTypeKey":
                    NotificationTypeMapping.getKey(setting.title),
                "emailNotification": setting.emailController.value ? 1 : 0,
                "osNotification": value ? 1 : 0,
              };

              final success =
                  await provider.updateNotificationSetting(context, payload);

              if (!success && context.mounted) {
                // Rollback on failure
                setting.osController.value = previousValue;
                _showErrorDialog(context, provider.errorMessage);
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildToggle(
    BuildContext context,
    ValueNotifier<bool> controller,
    ValueChanged<bool> onChanged,
  ) {
    return ValueListenableBuilder(
      valueListenable: controller,
      builder: (context, value, _) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(50),
            border: Border.all(color: AppConstants.primaryColor),
          ),
          child: AdvancedSwitch(
            height: 28,
            width: 60,
            controller: controller,
            initialValue: controller.value,
            thumb: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(color: AppConstants.primaryColor),
                borderRadius: BorderRadius.circular(50),
              ),
            ),
            borderRadius: BorderRadius.circular(20),
            activeColor: AppConstants.textGreenColor,
            inactiveColor: Colors.transparent,
            activeChild: Text(
              "Yes",
              style: lbItalic.copyWith(fontSize: 14),
            ),
            inactiveChild: Text(
              "No",
              style: lbItalic.copyWith(fontSize: 14),
            ),
            onChanged: (value) => onChanged(value as bool),
          ),
        );
      },
    );
  }

  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => AlertDialog(
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text("OK"),
          ),
        ],
      ),
    );
  }
}
