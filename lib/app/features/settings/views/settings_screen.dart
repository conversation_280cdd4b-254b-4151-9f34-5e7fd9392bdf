import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/app/core/widgets/app_button.dart';
import 'package:eljunto/app/core/widgets/configurable_popup.dart';
import 'package:eljunto/app/core/widgets/custom_scaffold.dart';
import 'package:eljunto/app/core/widgets/option_list_item.dart';
import 'package:eljunto/app/core/widgets/unified_app_bar.dart';
import 'package:eljunto/app/core/widgets/version_display.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../authentication/providers/auth_provider.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    void showLogoutDialog() {
      showDialog(
        context: context,
        barrierColor: Colors.white60,
        barrierDismissible: false,
        builder: (context) {
          return Consumer<AuthProvider>(
            builder: (context, authProvider, _) => ConfigurablePopupDialog(
              bodyText1: 'Logout',
              bodyText2: 'Are you sure you want to logout?',
              text1FontSize: 16,
              text2FontSize: 14,
              isButtonRow: true,
              buttons: [
                PopupButton(
                  text: 'Logout',
                  isLoading: authProvider.isLoading,
                  style: AppButtonStyle.solid,
                  onPressed: () async => await authProvider.logout(context),
                ),
                PopupButton(
                  text: 'Cancel',
                  style: AppButtonStyle.outlined,
                  onPressed: () => context.pop(),
                ),
              ],
            ),
          );
        },
      );
    }

    return CustomScaffold(
      appBar: UnifiedAppBar.main(
        title: 'Settings',
        showBackButton: true,
        onLeadingTap: () => context.pop(),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: SingleChildScrollView(
          child: Column(
            spacing: 25,
            children: [
              const SizedBox.shrink(),
              OptionListItem(
                label: 'Change Password',
                onTap: () => context.pushNamed('change-password'),
              ),
              OptionListItem(
                label: 'Change Email',
                onTap: () {
                  final currentEmail = SessionManager().userEmail;
                  context.pushNamed('change-email', extra: currentEmail);
                },
              ),
              OptionListItem(
                label: 'Notification Settings',
                onTap: () => context.pushNamed('notification-settings'),
              ),
              OptionListItem(
                label: 'Privacy Policy',
                onTap: () async =>
                    await launchUrl(AppConstants.privacyPolicyUrl),
              ),
              OptionListItem(
                label: 'Terms of Service',
                onTap: () async =>
                    await launchUrl(AppConstants.termsAndConditionUrl),
              ),
              OptionListItem(
                label: 'Manage Subscription',
                onTap: () => context.pushNamed('manage-subscription'),
              ),
              OptionListItem(
                label: 'Logout',
                onTap: () => showLogoutDialog(),
              ),
              OptionListItem(
                label: 'Delete Account',
                onTap: () => context.pushNamed('delete-account'),
                textColor: AppConstants.redColor,
              ),
              const VersionDisplay(),
              const SizedBox(height: 25),
            ],
          ),
        ),
      ),
    );
  }
}
