import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/app/core/widgets/app_button.dart';
import 'package:eljunto/app/core/widgets/configurable_popup.dart';
import 'package:eljunto/app/core/widgets/custom_scaffold.dart';
import 'package:eljunto/app/core/widgets/unified_app_bar.dart';
import 'package:eljunto/app/features/authentication/providers/auth_provider.dart';
import 'package:eljunto/models/club_charter_model.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

class DeleteAccountScreen extends StatefulWidget {
  const DeleteAccountScreen({super.key});

  @override
  State createState() => _DeleteAccountScreenState();
}

class _DeleteAccountScreenState extends State<DeleteAccountScreen> {
  int? userId;
  final _sessionManager = locator<SessionManager>();

  @override
  void initState() {
    _initializeUserId();
    super.initState();
  }

  Future<void> _initializeUserId() async {
    userId = _sessionManager.userId;
  }

  List<ClubCharterModel> information = [
    ClubCharterModel(
      rules: "Are you sure you want to permanently delete your account?",
    ),
    ClubCharterModel(
      rules: "This is irreversible.",
    ),
    ClubCharterModel(
      rules:
          "Deleting your account does not cancel a paid El Junto Subscription. You must manage your paid subscription through the platform or app store that you originally used to subscribe.",
    ),
    ClubCharterModel(
      rules:
          "All of your information will be deleted, and you will need to create a new account and resubscribe under that new account to use this app again.",
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      appBar: UnifiedAppBar.main(
        title: 'Delete Account',
        showBackButton: true,
        onLeadingTap: () => context.pop(),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          spacing: 20,
          children: [
            const SizedBox(height: 10),
            ...information.map(
              (policy) {
                return Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 3.0),
                      child: Align(
                        alignment: Alignment.topCenter,
                        child: Text(
                          "•",
                          textAlign: TextAlign.start,
                          style: lbRegular.copyWith(
                            fontSize: 20,
                            height: 0.8,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 5),
                    Expanded(
                      child: Text(
                        textAlign: TextAlign.start,
                        policy.rules ?? '',
                        style: lbRegular.copyWith(fontSize: 14),
                      ),
                    ),
                  ],
                );
              },
            ),
            AppButton(
              text: 'Delete Account',
              height: 40,
              borderRadius: 36,
              style: AppButtonStyle.destructive,
              onPressed: handleDeleteAccount,
            ),
            AppButton(
              text: 'Go Back',
              textColor: AppConstants.primaryColor,
              height: 40,
              borderRadius: 36,
              style: AppButtonStyle.outlined,
              onPressed: () => context.pop(),
            ),
          ],
        ),
      ),
    );
  }

  void handleDeleteAccount() {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Consumer<AuthProvider>(
          builder: (context, authProvider, _) => ConfigurablePopupDialog(
            bodyText1: 'Delete Account',
            bodyText2: 'Are you sure you want to delete your account?',
            text1FontSize: 14,
            text2FontSize: 12,
            isButtonRow: true,
            buttons: [
              PopupButton(
                text: 'Delete',
                textColor: AppConstants.redColor,
                textSize: 14,
                height: 28,
                style: AppButtonStyle.destructive,
                isLoading: authProvider.isLoading,
                onPressed: () => authProvider.deleteAccount(context),
              ),
              PopupButton(
                text: 'Cancel',
                textSize: 14,
                height: 28,
                style: AppButtonStyle.outlined,
                onPressed: () => context.pop(),
              ),
            ],
          ),
        );
      },
    );
  }
}
