import 'package:flutter/material.dart';

class NotificationSetting {
  String title;
  ValueNotifier<bool> emailController;
  ValueNotifier<bool> osController;
  bool showEmailController = true;
  bool showOSController = true;

  NotificationSetting(this.title, bool email, bool os, this.showEmailController,
      this.showOSController)
      : emailController = ValueNotifier<bool>(email),
        osController = ValueNotifier<bool>(os);

  // Method to update the controllers with new values (useful when re-fetching data)
  void updateControllers(bool email, bool os) {
    emailController.value = email;
    osController.value = os;
  }

  // Method to dispose of the ValueNotifiers when no longer needed
  void dispose() {
    emailController.dispose();
    osController.dispose();
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'emailController': emailController.value,
      'osController': osController.value,
    };
  }
}
