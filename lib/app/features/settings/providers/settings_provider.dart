import 'package:flutter/material.dart';

import '../model/settings_model.dart';
import '../services/settings_service.dart';

class SettingsProvider with ChangeNotifier {
  final SettingsService _settingsService = SettingsService();
  bool _isLoading = false;
  List<NotificationSetting> _notificationSettings = [];
  String _errorMessage = '';

  bool get isLoading => _isLoading;

  List<NotificationSetting> get notificationSettings => _notificationSettings;

  String get errorMessage => _errorMessage;

  Future<void> getNotificationSettings(BuildContext context) async {
    _isLoading = true;
    _errorMessage = '';
    notifyListeners();

    final newSettings = await _settingsService.getNotificationSettings(context);
    _errorMessage = _settingsService.errorMessage;

    // Dispose old settings to prevent memory leaks
    for (final setting in _notificationSettings) {
      setting.dispose();
    }

    _notificationSettings = newSettings;

    _isLoading = false;
    notifyListeners();
  }

  Future<bool> updateNotificationSetting(
      BuildContext context, Map<String, dynamic> payload) async {
    _isLoading = true;
    _errorMessage = '';
    notifyListeners();

    final success =
        await _settingsService.updateNotificationSetting(context, payload);
    _errorMessage = _settingsService.errorMessage;

    if (success) {
      await getNotificationSettings(context);
    }

    _isLoading = false;
    notifyListeners();
    return success;
  }

  Future<void> logout(BuildContext context) async {
    _isLoading = true;
    notifyListeners();

    // Dispose settings before logout
    for (final setting in _notificationSettings) {
      setting.dispose();
    }
    _notificationSettings.clear();

    await _settingsService.logout(context);
    _isLoading = false;
    notifyListeners();
  }

  @override
  void dispose() {
    // Dispose all settings when provider is disposed
    for (final setting in _notificationSettings) {
      setting.dispose();
    }
    super.dispose();
  }
}
