import 'dart:io' show Platform;

import 'package:eljunto/app/core/services/app_update_service.dart';
import 'package:eljunto/app/core/ui/dialog_helper.dart';
import 'package:eljunto/app/core/utils/app_config.dart';
import 'package:eljunto/app/core/utils/version_comparer.dart';
import 'package:eljunto/models/app_version_model.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';

enum UpdateStatus {
  idle,
  checking,
  noUpdateNeeded,
  optionalUpdateAvailable,
  mandatoryUpdateRequired,
  appInactive,
  error
}

class AppVersionProvider with ChangeNotifier {
  final _appUpdateService = AppUpdateService();

  String _currentAppVersion = "Unknown";

  String get currentAppVersion => _currentAppVersion;

  static const String _buildNumber = '18.10';

  String get buildNumber => _buildNumber;

  String _appName = '';

  String get appName => _appName;

  String get versionString => AppConfig.shared.flavor == Flavor.dev
      ? '$_currentAppVersion($_buildNumber)'
      : currentAppVersion;

  bool _isLoaded = false;

  bool get isLoaded => _isLoaded;

  UpdateStatus _updateStatus = UpdateStatus.idle;

  UpdateStatus get updateStatus => _updateStatus;

  String _errorMessage = "";

  String get errorMessage => _errorMessage;

  PlatformVersionInfo? _platformVersionInfo;

  PlatformVersionInfo? get platformVersionInfo => _platformVersionInfo;

  AppVersionProvider() {
    _loadCurrentAppVersion();
  }

  Future<void> _loadCurrentAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      _currentAppVersion = packageInfo.version;
      _appName = packageInfo.appName;
      _isLoaded = true;
    } catch (e) {
      _currentAppVersion = "Error loading version";
      _appName = "App";
    }
    notifyListeners();
  }

  Future<void> refreshVersionInfo() async {
    await _loadCurrentAppVersion();
  }

  Future<void> checkForUpdate(BuildContext context) async {
    _updateStatus = UpdateStatus.checking;
    _errorMessage = "";

    try {
      if (_currentAppVersion == "Unknown" ||
          _currentAppVersion.contains("Error")) {
        await _loadCurrentAppVersion();
      }
      final currentVersion = AppVersion.parse(_currentAppVersion);

      final config = await _appUpdateService.checkForUpdates();

      if (Platform.isAndroid) {
        _platformVersionInfo = config?.android;
      } else if (Platform.isIOS) {
        _platformVersionInfo = config?.ios;
      } else {
        _updateStatus = UpdateStatus.error;
        _errorMessage = "Unsupported platform.";
        notifyListeners();
        return;
      }

      if (_platformVersionInfo == null) {
        _updateStatus = UpdateStatus.error;
        _errorMessage = "Platform configuration not found.";
        notifyListeners();
        return;
      }

      if (_platformVersionInfo != null &&
          !(_platformVersionInfo?.status.active ?? false)) {
        _updateStatus = UpdateStatus.appInactive;
        notifyListeners();
        DialogHelper.appUpdateDialog(
          context,
          isMandatory: true,
          storeUrl: _platformVersionInfo?.downloadUrl ?? "",
        );
        return;
      }

      AppVersion minimumVersion;
      AppVersion latestVersion;
      try {
        minimumVersion =
            AppVersion.parse(_platformVersionInfo!.version.minimum);
        latestVersion = AppVersion.parse(_platformVersionInfo!.version.latest);
      } catch (e) {
        _updateStatus = UpdateStatus.error;
        _errorMessage = "Invalid version format: ${e.toString()}";
        notifyListeners();
        return;
      }
      if (_platformVersionInfo == null) {
        _updateStatus = UpdateStatus.error;
        _errorMessage = "Platform configuration not found.";
      } else if (currentVersion < minimumVersion) {
        _updateStatus = UpdateStatus.mandatoryUpdateRequired;
      } else if (currentVersion < latestVersion) {
        _updateStatus = UpdateStatus.optionalUpdateAvailable;
      } else {
        _updateStatus = UpdateStatus.noUpdateNeeded;
      }
    } catch (e) {
      _updateStatus = UpdateStatus.error;
      _errorMessage = "Failed to check for updates: ${e.toString()}";
      if (e is FormatException) {
        _errorMessage = "Version format error: ${e.message}";
      }
    }
    notifyListeners();

    // Show dialog based on status
    if (_updateStatus == UpdateStatus.mandatoryUpdateRequired) {
      DialogHelper.appUpdateDialog(
        context,
        isMandatory: true,
        storeUrl: _platformVersionInfo?.downloadUrl ?? "",
      );
    } else if (_updateStatus == UpdateStatus.optionalUpdateAvailable) {
      DialogHelper.appUpdateDialog(
        context,
        isMandatory: false,
        storeUrl: _platformVersionInfo?.downloadUrl ?? "",
      );
    }
  }
}
