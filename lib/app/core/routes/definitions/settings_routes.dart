import 'package:eljunto/app/core/widgets/transition_widget.dart';
import 'package:eljunto/app/features/authentication/views/change_email.dart';
import 'package:eljunto/app/features/authentication/views/change_password_screen.dart';
import 'package:eljunto/app/features/settings/views/delete_account_screen.dart';
import 'package:eljunto/app/features/settings/views/notification_settings_screen.dart';
import 'package:eljunto/app/features/settings/views/settings_screen.dart';
import 'package:eljunto/app/features/subscription/views/manage_subscription_screen.dart';
import 'package:eljunto/views/settings/block_list.dart';
import 'package:go_router/go_router.dart';

/// Defines the routes for the 'Settings' section of the application.
class SettingsRoutes {
  SettingsRoutes._();

  static final routes = [
    GoRoute(
      path: '/Settings',
      name: 'Settings',
      builder: (context, state) => SettingsScreen(key: state.pageKey),
      routes: [
        GoRoute(
          path: 'change-password',
          name: 'change-password',
          pageBuilder: (context, state) {
            return TransitionPageWidget.navigateTransitionPage(
              child: ChangePasswordScreen(
                key: state.pageKey,
              ),
            );
          },
        ),
        GoRoute(
          path: 'change-email',
          name: 'change-email',
          pageBuilder: (context, state) {
            final currentEmail = state.extra as String?;
            return TransitionPageWidget.navigateTransitionPage(
              child: ChangeEmail(
                key: state.pageKey,
                currentEmail: currentEmail,
              ),
            );
          },
        ),
        GoRoute(
          path: 'notification-settings',
          name: 'notification-settings',
          pageBuilder: (context, state) {
            return TransitionPageWidget.navigateTransitionPage(
              child: NotificationSettingsScreen(
                key: state.pageKey,
              ),
            );
          },
        ),
        GoRoute(
          path: 'block-list',
          name: 'block-list',
          pageBuilder: (context, state) {
            return TransitionPageWidget.navigateTransitionPage(
              child: BlockListPage(
                key: state.pageKey,
              ),
            );
          },
        ),
        GoRoute(
          path: 'manage-subscription',
          name: 'manage-subscription',
          pageBuilder: (context, state) {
            return TransitionPageWidget.navigateTransitionPage(
              child: ManageSubscriptionScreen(
                key: state.pageKey,
              ),
            );
          },
        ),
        GoRoute(
          path: 'delete-account',
          name: 'delete-account',
          pageBuilder: (context, state) {
            return TransitionPageWidget.navigateTransitionPage(
              child: DeleteAccountScreen(
                key: state.pageKey,
              ),
            );
          },
        ),
      ],
    ),
  ];
}
