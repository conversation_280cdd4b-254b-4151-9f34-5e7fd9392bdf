import 'package:eljunto/app/core/routes/definitions/settings_routes.dart';
import 'package:eljunto/app/core/widgets/transition_widget.dart';
import 'package:eljunto/models/user_model.dart';
import 'package:eljunto/views/profile/edit_profile/edit_profile_screen.dart';
import 'package:eljunto/views/profile/profile_home/profile_screen.dart';
import 'package:eljunto/views/profile/user_bookcase/edit_bookcase_screen.dart';
import 'package:eljunto/views/profile/user_bookcase/edit_current_reading_screen.dart';
import 'package:eljunto/views/profile/user_bookcase/to_be_read_screen.dart';
import 'package:go_router/go_router.dart';

import '../app_navigation_keys.dart';

/// Defines the routes for the 'Profile' section of the application.
class ProfileRoutes {
  ProfileRoutes._();

  static final StatefulShellBranch branch = StatefulShellBranch(
    navigatorKey: AppNavigationKeys.profileNavigatorKey,
    routes: [
      GoRoute(
        path: '/Profile',
        name: 'Profile',
        pageBuilder: (context, state) {
          return TransitionPageWidget.navigateTransitionPage(
            child: ProfileHomeScreen(
              key: state.pageKey,
            ),
          );
        },
        routes: [
          GoRoute(
            path: 'EditProfileScreen',
            name: 'EditProfileScreen',
            pageBuilder: (context, state) {
              final extras = state.extra as Map;

              final UserModel userModel = extras['userModel'] as UserModel;
              return TransitionPageWidget.navigateTransitionPage(
                child: EditProfileScreen(
                  key: state.pageKey,
                  buttonName: extras['buttonName'],
                  userModel: userModel,
                  updateProfile: extras['isCompleteProfile'],
                ),
              );
            },
          ),
          GoRoute(
            path: 'EditCurrentReadBook',
            name: 'EditCurrentReadBook',
            pageBuilder: (context, state) {
              final extras = state.extra as Map;
              return TransitionPageWidget.navigateTransitionPage(
                child: EditCurrentReadingBookScreen(
                  key: state.pageKey,
                  topShelfList: extras['topShelfList'],
                  buttonName: extras['buttonName'],
                  completedBookList: extras['completedBookList'],
                  userId: extras['userId'],
                ),
              );
            },
          ),
          GoRoute(
            path: 'EditBookCase',
            name: 'EditBookCase',
            pageBuilder: (context, state) {
              final caseData = state.extra as Map;
              return TransitionPageWidget.navigateTransitionPage(
                child: EditBookCaseScreen(
                  key: state.pageKey,
                  userName: caseData['userName'],
                  userHandler: caseData['handler'],
                  lengthofTopShelf: caseData['topShelfLength'],
                  userClubInvitation: caseData['userClubInvitation'],
                  userProfilePicture: caseData['userProfilePicture'],
                ),
              );
            },
          ),
          GoRoute(
            path: 'To-Be-Read',
            name: 'To-Be-Read',
            pageBuilder: (context, state) {
              return TransitionPageWidget.navigateTransitionPage(
                child: ToBeReadScreen(
                  key: state.pageKey,
                ),
              );
            },
          ),

          // Setting routes
          ...SettingsRoutes.routes,
        ],
      ),
    ],
  );
}
