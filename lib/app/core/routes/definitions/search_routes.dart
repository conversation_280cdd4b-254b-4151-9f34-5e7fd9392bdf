import 'package:eljunto/app/core/widgets/transition_widget.dart';
import 'package:eljunto/views/search/search_screen.dart';
import 'package:eljunto/views/search/show_club_screen.dart';
import 'package:go_router/go_router.dart';

import '../app_navigation_keys.dart';

/// Defines the routes for the 'Search' section of the application.
class SearchRoutes {
  SearchRoutes._();

  static final StatefulShellBranch branch = StatefulShellBranch(
    navigatorKey: AppNavigationKeys.searchNavigatorKey,
    routes: [
      GoRoute(
        path: '/Search',
        name: 'Search',
        builder: (context, state) => SearchScreen(
          key: state.pageKey,
        ),
        routes: [
          GoRoute(
            path: 'SearchResult',
            name: 'SearchResult',
            pageBuilder: (context, state) {
              final extras = state.extra as Map;

              return TransitionPageWidget.navigateTransitionPage(
                child: ShowClubScreen(
                  key: state.pageKey,
                  bookIds: extras['bookIds'],
                  filterName: extras['filterName'],
                  bookAuthor: extras['bookAuthor'],
                  bookName: extras['bookName'],
                ),
              );
            },
          ),
        ],
      ),
    ],
  );
}
