import 'package:go_router/go_router.dart';

import '../navigation/navigation_shell.dart';
import 'app_navigation_keys.dart';
import 'definitions/app_routes.dart';
import 'definitions/auth_routes.dart';
import 'definitions/clubs_routes.dart';
import 'definitions/home_routes.dart';
import 'definitions/message_routes.dart';
import 'definitions/profile_routes.dart';
import 'definitions/search_routes.dart';
import 'redirect_logic.dart';

/// The main router configuration for the application.
///
/// This class brings together all the separate routing components:
/// - Top-level routes
/// - Authentication routes
/// - Shell routes for the main app sections
/// - Redirect logic
class AppRouter {
  AppRouter._();

  static final GoRouter router = GoRouter(
    initialLocation: '/',
    navigatorKey: AppNavigationKeys.rootNavigatorKey,
    debugLogDiagnostics: true,
    redirect: AppRedirect.redirect,
    routes: [
      // Main application shell
      StatefulShellRoute.indexedStack(
        builder: (context, state, navigationShell) {
          return NavigationShell(
            navigationShell: navigationShell,
          );
        },
        branches: [
          HomeRoutes.branch,
          ProfileRoutes.branch,
          MessageRoutes.branch,
          ClubsRoutes.branch,
          SearchRoutes.branch,
        ],
      ),

      // Top-level routes
      ...AppRoutes.routes,

      // Authentication routes
      ...AuthRoutes.routes,
    ],
  );
}
