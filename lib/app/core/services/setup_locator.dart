import 'dart:developer';

import 'package:dio/dio.dart' as dio;
import 'package:eljunto/app/core/network/http/http_service.dart';
import 'package:eljunto/app/core/network/interceptors/http_interceptor.dart';
import 'package:eljunto/app/core/network/interceptors/retry_interceptor.dart';
import 'package:eljunto/app/core/providers/connectivity_provider.dart';
import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:get_it/get_it.dart';

final locator = GetIt.instance;

void setupLocator() {
  log('--- Setting up Locator ---');

  // 1. Register ConnectivityProvider (Singleton) - MUST BE FIRST if others depend on it
  locator.registerLazySingleton<ConnectivityProvider>(
    () {
      log('Registering ConnectivityProvider...');
      final provider = ConnectivityProvider();
      provider.startListening();
      log('ConnectivityProvider registered and listening.');
      return provider;
    },
  );

  locator.registerLazySingleton<SessionManager>(
    () {
      final provider = SessionManager();
      provider.initialize();
      return provider;
    },
  );

  // 2. Register Dio (Singleton) - Configure it fully here
  locator.registerLazySingleton<dio.Dio>(() {
    log('Registering Dio...');
    final dioInstance = dio.Dio();

    // Fetch the already registered ConnectivityProvider
    final connectivity = locator<ConnectivityProvider>();
    log('Adding interceptors to Dio...');

    // Add Interceptors IN ORDER
    dioInstance.interceptors.add(HttpInterceptor());
    log('Added HttpInterceptor.');

    // Add the Retry Interceptor
    dioInstance.interceptors.add(
      RetryOnConnectionChangeInterceptor(
        dio: dioInstance,
        connectivityProvider: connectivity,
      ),
    );
    log('Added RetryOnConnectionChangeInterceptor.');

    // // Add Log Interceptor (consider adding this last for onError logging)
    // dioInstance.interceptors.add(
    //   dio.LogInterceptor(
    //     request: true,
    //     requestHeader: true,
    //     requestBody: true,
    //     responseHeader: true,
    //     responseBody: true,
    //     error: true, // Log errors too
    //     logPrint: (object) => log(object.toString()),
    //   ),
    // );
    // log('Added LogInterceptor.');
    log('Dio instance configured and registered.');
    return dioInstance;
  });

  // 3. Register your HttpApiService (Injecting the configured Dio)
  locator.registerLazySingleton<HttpApiService>(() {
    log('Registering HttpApiService...');
    final service = HttpApiService(
      dioInstance: locator<dio.Dio>(),
    );
    log('HttpApiService registered.');
    return service;
  });

  log('--- Locator setup complete ---');
}
