import 'package:flutter/material.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

import '../../../reusableWidgets/connection_error/connection_lost_screen.dart';
import '../constants.dart';
import '../providers/connectivity_provider.dart';
import 'setup_locator.dart';

class ConnectivityService {
  Future<void> checkInternetConnectivity(
      {required BuildContext context, required VoidCallback onTryAgain}) async {
    showModalBottomSheet(
      context: context,
      isScrollControlled: false,
      isDismissible: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      barrierColor: AppConstants.backgroundColor.withValues(alpha: .5),
      sheetAnimationStyle: AnimationStyle(
        duration: Duration(milliseconds: 300),
        curve: Curves.elasticIn,
        reverseDuration: Duration(milliseconds: 300),
        reverseCurve: Curves.elasticOut,
      ),
      builder: (context) => ConnectivityLossSheet(
        onTryAgain: onTryAgain,
      ),
    );
  }

  static void networkClose(Future<void> loadMoreResult, BuildContext context) {
    final provider = locator<ConnectivityProvider>();
    if (provider.status == InternetStatus.connected) {
      loadMoreResult;
    } else {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        isDismissible: true,
        enableDrag: true,
        backgroundColor: Colors.transparent,
        useRootNavigator: true,
        barrierColor: AppConstants.backgroundColor.withValues(alpha: .5),
        sheetAnimationStyle: AnimationStyle(
          duration: Duration(milliseconds: 300),
          curve: Curves.elasticIn,
          reverseDuration: Duration(milliseconds: 300),
          reverseCurve: Curves.elasticOut,
        ),
        builder: (context) => SingleChildScrollView(
          child: ConnectivityLossSheet(
              // onTryAgain: clubOpeningRefresh,
              ),
        ),
      );
    }
    if (provider.status == InternetStatus.connected) {
      loadMoreResult;
    }
  }
}
