import 'package:eljunto/app/core/enums/enums.dart';
import 'package:eljunto/app/core/routes/app_navigation_keys.dart';
import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/controller/message_controller.dart';
import 'package:eljunto/models/clubs_model/clubs_screen4_model/incoming_outgoing_request.dart';
import 'package:eljunto/reusable_api_function/club/club_function.dart';
import 'package:flutter/material.dart';

class NavigationProvider extends ChangeNotifier {
  final MessageController _messageController;
  final BookClubController _bookClubController;
  final ClubController _clubController;
  final SessionManager _sessionManager;

  NavigationProvider(
    this._messageController,
    this._bookClubController,
    this._clubController,
    this._sessionManager,
  ) {
    _messageController.addListener(_onDataChanged);
    _initializeGlobalData();
  }

  bool get hasMessageNotification => _messageController.hasNewNotification;
  bool get hasClubNotification =>
      _messageController.hasNewClubActivityNotifications;
  final _key = AppNavigationKeys.rootNavigatorKey;

  Future<void> _initializeGlobalData() async {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final loggedinUserId = _sessionManager.userId;
      if (loggedinUserId != null) {
        await _messageController.initializeMessagesData(loggedinUserId);
        await Future.wait(
          [
            _getPendingInvitations(loggedinUserId),
            _getOutGoingRequest(loggedinUserId),
            _getStandingBookClubsByUserId(loggedinUserId, false),
            _getImpromptuBookClubsByUserId(loggedinUserId, false),
          ],
        );
      }
    });
  }

  Future<void> _getPendingInvitations(int userId) async {
    await _bookClubController.inComingRequest(
      userId,
      ClubMembershipStatus.pending.value,
      ClubMembershipStatus.reOpened.value,
      ClubRequestType.incomingClubRequestByUserId.value,
      _key.currentContext!,
    );
    final pendingInvitations =
        _bookClubController.incomingOutGoingRequest?.data;
    await _messageController
        .incomingClubInvitationStatus(pendingInvitations?.isNotEmpty ?? false);
  }

  Future<void> _getOutGoingRequest(int userId) async {
    await _bookClubController.inComingRequest(
      userId,
      ClubMembershipStatus.pending.value,
      ClubMembershipStatus.reOpened.value,
      ClubRequestType.outgoingClubRequestByUserId.value,
      _key.currentContext!,
    );
    final outgoingRequestList =
        _bookClubController.incomingOutGoingRequest?.data;
    bool hasNewOutgoingRequests = _checkStatus(outgoingRequestList);
    await _messageController.updateOutgoingRequests(hasNewOutgoingRequests);
  }

  bool _checkStatus(List<RequestManage>? outgoingRequestList) {
    if (outgoingRequestList?.isNotEmpty ?? false) {
      for (var element in outgoingRequestList!) {
        if (element.status == "REOPENED") {
          return true;
        }
      }
    }
    return false;
  }

  Future<void> _getStandingBookClubsByUserId(int userId, bool isMore) async {
    await _clubController.getBookClubsByUserId(
      _key.currentContext!,
      userId,
      ClubType.standing.value,
      isMore,
    );
    final standingBookClubList = _clubController.standingBookClubList;
    bool hasNewStandingRequests = standingBookClubList?.any((element) =>
            element.incomingRequest == true && element.userId == userId) ??
        false;
    await _messageController.updateStandingClubRequests(hasNewStandingRequests);
  }

  Future<void> _getImpromptuBookClubsByUserId(int userId, bool isMore) async {
    await _clubController.getBookClubsByUserId(
      _key.currentContext!,
      userId,
      ClubType.impromptu.value,
      isMore,
    );
    final impromptuBookClubList = _clubController.impromptuBookClubList;
    bool hasNewImpromptuRequests = impromptuBookClubList?.any((element) =>
            element.incomingRequest == true && element.userId == userId) ??
        false;
    await _messageController
        .updateImpromptuClubRequests(hasNewImpromptuRequests);
  }

  void _onDataChanged() {
    notifyListeners();
  }

  @override
  void dispose() {
    _messageController.removeListener(_onDataChanged);
    super.dispose();
  }
}
