import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class OptionListItem extends StatelessWidget {
  final String label;
  final VoidCallback onTap;
  final Color? textColor;

  const OptionListItem({
    super.key,
    required this.label,
    required this.onTap,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return NetworkAwareTap(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        height: 55,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: textColor != AppConstants.redColor
                ? AppConstants.primaryColor
                : AppConstants.redColor,
            width: 1.5,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          spacing: 10,
          children: [
            Expanded(
              child: MarqueeList(
                children: [
                  Text(
                    label,
                    overflow: TextOverflow.ellipsis,
                    style: lbRegular.copyWith(
                      fontSize: 18,
                      color: textColor,
                    ),
                  ),
                ],
              ),
            ),
            textColor != AppConstants.redColor
                ? SvgPicture.asset(
                    'assets/icons/svg/Vector.svg',
                    height: 17,
                    width: 17,
                    fit: BoxFit.cover,
                  )
                : SvgPicture.asset(
                    'assets/icons/svg/Vector.svg',
                    height: 17,
                    width: 17,
                    fit: BoxFit.cover,
                    colorFilter: ColorFilter.mode(
                      AppConstants.redColor,
                      BlendMode.srcIn,
                    ),
                  ),
          ],
        ),
      ),
    );
  }
}
