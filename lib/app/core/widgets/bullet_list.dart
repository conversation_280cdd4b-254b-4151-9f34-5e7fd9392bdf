import 'package:flutter/material.dart';

import '../utils/text_style.dart';

class BulletList extends StatelessWidget {
  final String pointText;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisAlignment mainAxisAlignment;

  const BulletList({
    super.key,
    required this.pointText,
    this.crossAxisAlignment = CrossAxisAlignment.start,
    this.mainAxisAlignment = MainAxisAlignment.center,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: crossAxisAlignment,
      mainAxisAlignment: mainAxisAlignment,
      children: [
        Icon(
          Icons.circle,
          color: Colors.black.withValues(alpha: .7),
          size: 14,
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Text(
            pointText,
            style: lbRegular.copyWith(fontSize: 14),
          ),
        ),
      ],
    );
  }
}
