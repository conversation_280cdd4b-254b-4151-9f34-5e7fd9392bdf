import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:flutter/material.dart';

class AppErrorMessage extends StatelessWidget {
  final String errorMessage;
  final double fontSize;
  final FontWeight fontWeight;

  const AppErrorMessage({
    super.key,
    required this.errorMessage,
    required this.fontSize,
    this.fontWeight = FontWeight.w400,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      errorMessage,
      style: lbRegular.copyWith(
        fontSize: fontSize,
        color: AppConstants.redColor,
        fontWeight: fontWeight,
      ),
    );
  }
}
