import 'package:eljunto/app/features/authentication/views/widgets/auth_form_field.dart';
import 'package:eljunto/app/features/authentication/views/widgets/auth_otp_field.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../reusableWidgets/connection_error/network_aware_tap.dart';
import '../constants.dart';
import '../utils/text_style.dart';
import 'app_button.dart';

class PopupField {
  final String? label;
  final String? placeholder;
  final TextEditingController? controller;
  final TextInputType? keyboardType;
  final int? maxLines;
  final int? maxLength;
  final bool isRequired;
  final String? validationMessage;
  final FocusNode? focusNode;
  final Function(String)? onChanged;
  final bool? isTextAreaForOtp;

  const PopupField({
    this.label,
    this.placeholder,
    this.controller,
    this.keyboardType,
    this.maxLines = 1,
    this.maxLength,
    this.isRequired = false,
    this.validationMessage,
    this.focusNode,
    this.onChanged,
    this.isTextAreaForOtp = false,
  });
}

class PopupDropdownOption {
  final String value;
  final String label;

  const PopupDropdownOption({
    required this.value,
    required this.label,
  });
}

class PopupButton {
  final String text;
  final Color textColor;
  final double textSize;
  final VoidCallback? onPressed;
  final AppButtonStyle style;
  final bool isLoading;
  final double? height;
  final double? borderRadius;

  const PopupButton({
    required this.text,
    this.textColor = AppConstants.primaryColor,
    this.textSize = 14,
    this.onPressed,
    this.style = AppButtonStyle.solid,
    this.isLoading = false,
    this.height,
    this.borderRadius,
  });
}

class ConfigurablePopupDialog extends StatefulWidget {
  final String? headerText;
  final String? bodyText1;
  final String? bodyText2;
  final double text1FontSize;
  final double text2FontSize;

  // Search field
  final PopupField? searchField;

  // Dropdown field
  final String? dropdownLabel;
  final List<PopupDropdownOption>? dropdownOptions;
  final String? selectedDropdownValue;
  final ValueChanged<String?>? onDropdownChanged;

  // Date picker
  final String? dateLabel;
  final DateTime? selectedDate;
  final ValueChanged<DateTime>? onDateChanged;

  // Radio button
  final String? radioLabel;
  final bool? radioValue;
  final ValueChanged<bool>? onRadioChanged;

  // Rating
  final String? ratingLabel;
  final double? initialRating;
  final ValueChanged<double>? onRatingChanged;

  // Text area
  final PopupField? textArea;

  // Buttons
  final bool isButtonRow;
  final List<PopupButton>? buttons;

  // Close callback
  final VoidCallback? onClose;

  // Validation
  final Map<String, bool>? validationErrors;
  final VoidCallback? onValidationChanged;

  const ConfigurablePopupDialog({
    super.key,
    this.headerText,
    this.bodyText1,
    this.bodyText2,
    this.text1FontSize = 14,
    this.text2FontSize = 14,
    this.searchField,
    this.dropdownLabel,
    this.dropdownOptions,
    this.selectedDropdownValue,
    this.onDropdownChanged,
    this.dateLabel,
    this.selectedDate,
    this.onDateChanged,
    this.radioLabel,
    this.radioValue,
    this.onRadioChanged,
    this.ratingLabel,
    this.initialRating,
    this.onRatingChanged,
    this.textArea,
    this.buttons,
    this.isButtonRow = false,
    this.onClose,
    this.validationErrors,
    this.onValidationChanged,
  });

  @override
  State<ConfigurablePopupDialog> createState() =>
      _ConfigurablePopupDialogState();
}

class _ConfigurablePopupDialogState extends State<ConfigurablePopupDialog> {
  final List<TextEditingController> _controllers = [];
  final List<FocusNode> _focusNodes = [];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    // Initialize controllers for search field and text area
    if (widget.searchField?.controller != null) {
      _controllers.add(widget.searchField!.controller!);
      _focusNodes.add(FocusNode());
    }
    if (widget.textArea?.controller != null) {
      _controllers.add(widget.textArea!.controller!);
      _focusNodes.add(FocusNode());
    }
  }

  @override
  void dispose() {
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  void _handleClose() {
    // Clear focus to dismiss keyboard
    FocusManager.instance.primaryFocus?.unfocus();
    widget.onClose?.call();
    if (context.mounted) {
      context.pop();
    }
  }

  Widget _buildCloseButton() {
    return Align(
      alignment: Alignment.centerRight,
      child: NetworkAwareTap(
        onTap: _handleClose,
        child: Image.asset(
          AppConstants.closePopupImagePath,
          height: 26,
          width: 26,
        ),
      ),
    );
  }

  Widget _buildHeaderText() {
    if (widget.headerText == null) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10.0),
      child: Text(
        widget.headerText!,
        textAlign: TextAlign.center,
        style: lbRegular.copyWith(fontSize: 18),
      ),
    );
  }

  Widget _buildBodyText(String text, double fontSize) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10.0),
      child: Text(
        text,
        textAlign: TextAlign.center,
        style: lbRegular.copyWith(fontSize: fontSize),
      ),
    );
  }

  Widget _buildSearchField() {
    if (widget.searchField == null) return const SizedBox.shrink();

    final field = widget.searchField!;
    final hasError = widget.validationErrors?[field.label ?? 'search'] ?? false;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (field.label != null) ...[
          Text(
            field.label!,
            style: lbRegular.copyWith(fontSize: 14),
          ),
          const SizedBox(height: 8),
        ],
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: hasError
                  ? AppConstants.redColor
                  : AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          child: TextField(
            controller: field.controller,
            focusNode: _focusNodes.isNotEmpty ? _focusNodes[0] : null,
            keyboardType: field.keyboardType ?? TextInputType.text,
            maxLines: field.maxLines,
            maxLength: field.maxLength,
            decoration: InputDecoration(
              hintText: field.placeholder ?? 'Search',
              hintStyle: lbRegular.copyWith(
                color: AppConstants.hintTextColor,
                fontSize: 14,
              ),
              prefixIcon: const Icon(
                Icons.search,
                color: AppConstants.primaryColor,
                size: 20,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            style: lbRegular.copyWith(fontSize: 14),
          ),
        ),
        if (hasError && field.validationMessage != null) ...[
          const SizedBox(height: 4),
          Text(
            field.validationMessage!,
            style: lbRegular.copyWith(
              color: AppConstants.redColor,
              fontSize: 12,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDropdown() {
    if (widget.dropdownOptions == null || widget.dropdownOptions!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.dropdownLabel != null) ...[
          Text(
            widget.dropdownLabel!,
            style: lbRegular.copyWith(fontSize: 14),
          ),
          const SizedBox(height: 8),
        ],
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          child: DropdownButtonFormField<String>(
            value: widget.selectedDropdownValue,
            decoration: const InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            hint: Text(
              'Select your club',
              style: lbRegular.copyWith(
                color: AppConstants.hintTextColor,
                fontSize: 14,
              ),
            ),
            icon: const Icon(
              Icons.keyboard_arrow_down,
              color: AppConstants.primaryColor,
            ),
            items: widget.dropdownOptions!.map((option) {
              return DropdownMenuItem<String>(
                value: option.value,
                child: Text(
                  option.label,
                  style: lbRegular.copyWith(fontSize: 14),
                ),
              );
            }).toList(),
            onChanged: widget.onDropdownChanged,
            style: lbRegular.copyWith(fontSize: 14),
          ),
        ),
      ],
    );
  }

  Widget _buildDatePicker() {
    if (widget.onDateChanged == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.dateLabel != null) ...[
          Text(
            widget.dateLabel!,
            style: lbRegular.copyWith(fontSize: 14),
          ),
          const SizedBox(height: 8),
        ],
        Row(
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppConstants.popUpBorderColor,
                    width: 1.5,
                  ),
                ),
                child: InkWell(
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: widget.selectedDate ?? DateTime.now(),
                      firstDate: DateTime.now(),
                      lastDate: DateTime.now().add(const Duration(days: 365)),
                    );
                    if (date != null) {
                      widget.onDateChanged!(date);
                    }
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            widget.selectedDate != null
                                ? '${widget.selectedDate!.day}/${widget.selectedDate!.month}/${widget.selectedDate!.year}'
                                : 'Select date...',
                            style: lbRegular.copyWith(
                              color: widget.selectedDate != null
                                  ? AppConstants.primaryColor
                                  : AppConstants.hintTextColor,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        const Icon(
                          Icons.calendar_today,
                          color: AppConstants.primaryColor,
                          size: 20,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            if (widget.radioLabel != null) ...[
              const SizedBox(width: 16),
              Row(
                children: [
                  Radio<bool>(
                    value: true,
                    groupValue: widget.radioValue,
                    onChanged: (value) {
                      if (value != null) {
                        widget.onRadioChanged?.call(value);
                      }
                    },
                    activeColor: AppConstants.textGreenColor,
                  ),
                  Text(
                    widget.radioLabel!,
                    style: lbRegular.copyWith(fontSize: 14),
                  ),
                ],
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildRating() {
    if (widget.onRatingChanged == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.ratingLabel != null) ...[
          Text(
            widget.ratingLabel!,
            style: lbRegular.copyWith(fontSize: 14),
          ),
          const SizedBox(height: 8),
        ],
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(5, (index) {
            final starValue = index + 1.0;
            final isSelected = starValue <= (widget.initialRating ?? 0);

            return GestureDetector(
              onTap: () {
                widget.onRatingChanged!(starValue);
              },
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: Icon(
                  isSelected ? Icons.star : Icons.star_border,
                  color: AppConstants.textGreenColor,
                  size: 24,
                ),
              ),
            );
          }),
        ),
      ],
    );
  }

  Widget _buildTextArea() {
    if (widget.textArea == null) return const SizedBox.shrink();

    final field = widget.textArea!;
    final hasError =
        widget.validationErrors?[field.label ?? 'textarea'] ?? false;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (field.label != null) ...[
          Text(
            field.label!,
            style: lbRegular.copyWith(fontSize: 14),
          ),
          const SizedBox(height: 8),
        ],
        (field.isTextAreaForOtp ?? false)
            ? Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AuthOtpField(
                    otpController: field.controller!,
                    focusNode: field.focusNode!,
                    onChanged: field.onChanged,
                    errorMessage: field.validationMessage,
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        field.validationMessage ?? '',
                        style: lbRegular.copyWith(
                          fontSize: 12,
                          color: AppConstants.redColor,
                        ),
                      ),
                    ],
                  ),
                ],
              )
            : AuthFormField(
                controller: field.controller!,
                focusNode: _focusNodes.length > 1 ? _focusNodes[1] : null,
                keyboardType: field.keyboardType ?? TextInputType.multiline,
                maxLines: field.maxLines ?? 4,
                maxLength: field.maxLength,
                hintText: field.placeholder ?? '',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'This field is required';
                  }
                  return null;
                },
              ),
        if (hasError && field.validationMessage != null) ...[
          const SizedBox(height: 4),
          Text(
            field.validationMessage!,
            style: lbRegular.copyWith(
              color: AppConstants.redColor,
              fontSize: 12,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildButtons() {
    final buttons = widget.buttons ?? [];
    if (buttons.isEmpty) return const SizedBox.shrink();

    // Check if any button is loading
    final loadingIndex = buttons.indexWhere((b) => b.isLoading);

    if (loadingIndex != -1) {
      // Only show the loading button, centered
      return Center(
        child: _buildSingleButton(buttons[loadingIndex]),
      );
    }

    // 2 buttons: row
    if (buttons.length == 2) {
      return Row(
        children: [
          Expanded(child: _buildSingleButton(buttons[0])),
          const SizedBox(width: 10),
          Expanded(child: _buildSingleButton(buttons[1])),
        ],
      );
    }

    // 3 buttons: 2x1 grid
    if (buttons.length == 3) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Expanded(child: _buildSingleButton(buttons[0])),
              const SizedBox(width: 10),
              Expanded(child: _buildSingleButton(buttons[1])),
            ],
          ),
          const SizedBox(height: 10),
          _buildSingleButton(buttons[2]),
        ],
      );
    }

    // 1 button: just show it
    if (buttons.length == 1) {
      return _buildSingleButton(buttons[0]);
    }

    // Default: stack vertically
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: buttons.map(_buildSingleButton).toList(),
    );
  }

  Widget _buildSingleButton(PopupButton button) => Padding(
        padding: const EdgeInsets.only(top: 12),
        child: AppButton(
          text: button.text,
          textSize: button.textSize,
          textColor: button.textColor,
          onPressed: button.onPressed,
          style: button.style,
          isLoading: button.isLoading,
          isEnabled: true,
          width: double.infinity,
          height: button.height ?? 40,
          borderRadius: button.borderRadius ?? 36,
        ),
      );

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: AlertDialog(
        insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 0),
        contentPadding: const EdgeInsets.fromLTRB(
          25.0,
          15.0,
          25.0,
          0,
        ),
        actionsPadding: EdgeInsets.fromLTRB(
          20,
          10,
          20,
          30,
        ),
        backgroundColor: AppConstants.backgroundColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(28),
          side: const BorderSide(
            color: AppConstants.popUpBorderColor,
            width: 1.5,
          ),
        ),
        surfaceTintColor: Colors.white,
        content: SingleChildScrollView(
          child: SizedBox(
            width: MediaQuery.sizeOf(context).width * .90,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 10),
                _buildCloseButton(),
                const SizedBox(height: 5),
                if (widget.headerText != null) ...[
                  _buildHeaderText(),
                  const SizedBox(height: 25),
                ],
                if (widget.bodyText1 != null) ...[
                  _buildBodyText(widget.bodyText1!, widget.text1FontSize),
                  const SizedBox(height: 20),
                ],
                if (widget.searchField != null) ...[
                  _buildSearchField(),
                  const SizedBox(height: 20),
                ],
                if (widget.bodyText2 != null) ...[
                  _buildBodyText(widget.bodyText2!, widget.text2FontSize),
                  const SizedBox(height: 20),
                ],
                if (widget.dropdownOptions != null) ...[
                  _buildDropdown(),
                  const SizedBox(height: 20),
                ],
                if (widget.onDateChanged != null ||
                    widget.radioLabel != null) ...[
                  _buildDatePicker(),
                  const SizedBox(height: 20),
                ],
                if (widget.onRatingChanged != null) ...[
                  _buildRating(),
                  const SizedBox(height: 20),
                ],
                if (widget.textArea != null) ...[
                  _buildTextArea(),
                  const SizedBox(height: 20),
                ],
              ],
            ),
          ),
        ),
        actions: [
          if (widget.buttons != null) _buildButtons(),
        ],
      ),
    );
  }
}

// Helper function to show the popup
Future<T?> showConfigurablePopup<T>({
  required BuildContext context,
  String? headerText,
  String? bodyText1,
  String? bodyText2,
  double text1FontSize = 14,
  double text2FontSize = 14,
  PopupField? searchField,
  String? dropdownLabel,
  List<PopupDropdownOption>? dropdownOptions,
  String? selectedDropdownValue,
  ValueChanged<String?>? onDropdownChanged,
  String? dateLabel,
  DateTime? selectedDate,
  ValueChanged<DateTime>? onDateChanged,
  String? radioLabel,
  bool? radioValue,
  ValueChanged<bool>? onRadioChanged,
  String? ratingLabel,
  double? initialRating,
  ValueChanged<double>? onRatingChanged,
  PopupField? textArea,
  List<PopupButton>? buttons,
  bool isButtonRow = false,
  VoidCallback? onClose,
  Map<String, bool>? validationErrors,
  VoidCallback? onValidationChanged,
}) {
  return showDialog<T>(
    barrierColor: Colors.white60,
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return ConfigurablePopupDialog(
        headerText: headerText,
        bodyText1: bodyText1,
        bodyText2: bodyText2,
        text1FontSize: text1FontSize,
        text2FontSize: text2FontSize,
        searchField: searchField,
        dropdownLabel: dropdownLabel,
        dropdownOptions: dropdownOptions,
        selectedDropdownValue: selectedDropdownValue,
        onDropdownChanged: onDropdownChanged,
        dateLabel: dateLabel,
        selectedDate: selectedDate,
        onDateChanged: onDateChanged,
        radioLabel: radioLabel,
        radioValue: radioValue,
        onRadioChanged: onRadioChanged,
        ratingLabel: ratingLabel,
        initialRating: initialRating,
        onRatingChanged: onRatingChanged,
        textArea: textArea,
        buttons: buttons,
        isButtonRow: isButtonRow,
        onClose: onClose,
        validationErrors: validationErrors,
        onValidationChanged: onValidationChanged,
      );
    },
  );
}
