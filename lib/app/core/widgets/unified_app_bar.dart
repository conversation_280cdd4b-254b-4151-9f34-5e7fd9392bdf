import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/app/core/widgets/query_feedback_dialog.dart';
import 'package:eljunto/reusableWidgets/cached_network_image.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';

/// A unified, configurable AppBar widget that consolidates all AppBar functionality
/// from the El Junto app into a single, reusable component.
///
/// This widget supports multiple AppBar variants through named constructors:
/// - [UnifiedAppBar.home()] - Main app bar with logo and question mark
/// - [UnifiedAppBar.profile()] - Profile app bar with back button and user info
/// - [UnifiedAppBar.profileHome()] - Profile home with logo and user info
/// - [UnifiedAppBar.message()] - Message screen with complex title
/// - [UnifiedAppBar.previous()] - Previous screen with back navigation
/// - [UnifiedAppBar.custom()] - Fully customizable app bar
class UnifiedAppBar extends StatefulWidget implements PreferredSizeWidget {
  // Core properties
  final String? title;
  final String? subtitle;
  final Widget? leading;
  final List<Widget>? actions;
  final bool centerTitle;
  final Color? backgroundColor;
  final double? toolbarHeight;
  final VoidCallback? onLeadingTap;
  final VoidCallback? onQuestionTap;

  // Profile-specific properties
  final String? userName;
  final String? userHandle;
  final String? userProfilePicture;
  final bool isOpenToClubInvitation;
  final bool userOwnProfile;

  // Message-specific properties
  final String? bookName;
  final String? clubName;
  final String? impromptuCount;
  final bool showImpromptu;
  final bool clubNameFlag;
  final List<dynamic>? clubMembers;
  final VoidCallback? onTitleTap;

  // Navigation properties
  final bool showBackButton;
  final bool showLogo;
  final String? backRoute;

  // Search properties
  final String? searchBookName;
  final String? searchAuthorName;
  final bool showSearchResults;

  // Customization
  final bool showQuestionMark;
  final bool useSkeleton;
  final EdgeInsets? titlePadding;
  final EdgeInsets? leadingPadding;
  final EdgeInsets? actionsPadding;

  const UnifiedAppBar({
    super.key,
    this.title,
    this.subtitle,
    this.leading,
    this.actions,
    this.centerTitle = true,
    this.backgroundColor,
    this.toolbarHeight,
    this.onLeadingTap,
    this.onQuestionTap,
    this.userName,
    this.userHandle,
    this.userProfilePicture,
    this.isOpenToClubInvitation = false,
    this.userOwnProfile = false,
    this.bookName,
    this.clubName,
    this.impromptuCount,
    this.showImpromptu = false,
    this.clubNameFlag = false,
    this.clubMembers,
    this.onTitleTap,
    this.showBackButton = false,
    this.showLogo = false,
    this.backRoute,
    this.searchBookName,
    this.searchAuthorName,
    this.showSearchResults = false,
    this.showQuestionMark = true,
    this.useSkeleton = true,
    this.titlePadding,
    this.leadingPadding,
    this.actionsPadding,
  });

  /// Creates a main AppBar that can act as either home or previous screen AppBar
  const UnifiedAppBar.main({
    super.key,
    String? title,
    this.subtitle,
    this.showBackButton = false,
    this.showLogo = false,
    this.backRoute,
    this.onLeadingTap,
    this.onQuestionTap,
    this.useSkeleton = true,
    // Optionally allow search and impromptu fields for previous screen
    this.searchBookName,
    this.searchAuthorName,
    this.showSearchResults = false,
    this.impromptuCount,
    this.showImpromptu = false,
    this.clubName,
    this.clubNameFlag = false,
  })  : title = title ?? (showLogo ? AppConstants.appName : null),
        leading = null,
        actions = null,
        centerTitle = true,
        backgroundColor = null,
        toolbarHeight = 80,
        userName = null,
        userHandle = null,
        userProfilePicture = null,
        isOpenToClubInvitation = false,
        userOwnProfile = false,
        bookName = null,
        clubMembers = null,
        onTitleTap = null,
        showQuestionMark = true,
        titlePadding = null,
        leadingPadding = null,
        actionsPadding = null;

  /// Creates a profile AppBar with back button and user information
  const UnifiedAppBar.profile({
    super.key,
    this.userName,
    this.userHandle,
    this.userProfilePicture,
    this.isOpenToClubInvitation = false,
    this.userOwnProfile = false,
    this.onLeadingTap,
    this.onQuestionTap,
    this.useSkeleton = true,
  })  : title = null,
        subtitle = null,
        leading = null,
        actions = null,
        centerTitle = true,
        backgroundColor = null,
        toolbarHeight = null,
        bookName = null,
        clubName = null,
        impromptuCount = null,
        showImpromptu = false,
        clubNameFlag = false,
        clubMembers = null,
        onTitleTap = null,
        showBackButton = true,
        showLogo = false,
        backRoute = null,
        searchBookName = null,
        searchAuthorName = null,
        showSearchResults = false,
        showQuestionMark = true,
        titlePadding = null,
        leadingPadding = null,
        actionsPadding = null;

  /// Creates a profile home AppBar with logo and user information
  const UnifiedAppBar.profileHome({
    super.key,
    this.userName,
    this.userHandle,
    this.userProfilePicture,
    this.isOpenToClubInvitation = false,
    this.userOwnProfile = false,
    this.onQuestionTap,
    this.useSkeleton = true,
  })  : title = null,
        subtitle = null,
        leading = null,
        actions = null,
        centerTitle = true,
        backgroundColor = null,
        toolbarHeight = null,
        onLeadingTap = null,
        bookName = null,
        clubName = null,
        impromptuCount = null,
        showImpromptu = false,
        clubNameFlag = false,
        clubMembers = null,
        onTitleTap = null,
        showBackButton = false,
        showLogo = true,
        backRoute = null,
        searchBookName = null,
        searchAuthorName = null,
        showSearchResults = false,
        showQuestionMark = true,
        titlePadding = null,
        leadingPadding = null,
        actionsPadding = null;

  /// Creates a message AppBar with complex title and navigation
  const UnifiedAppBar.message({
    super.key,
    this.bookName,
    this.clubName,
    this.impromptuCount,
    this.showImpromptu = false,
    this.clubNameFlag = false,
    this.clubMembers,
    this.onTitleTap,
    this.showBackButton = true,
    this.backRoute,
    this.onQuestionTap,
  })  : title = null,
        subtitle = null,
        leading = null,
        actions = null,
        centerTitle = true,
        backgroundColor = null,
        toolbarHeight = null,
        onLeadingTap = null,
        userName = null,
        userHandle = null,
        userProfilePicture = null,
        isOpenToClubInvitation = false,
        userOwnProfile = false,
        showLogo = false,
        searchBookName = null,
        searchAuthorName = null,
        showSearchResults = false,
        showQuestionMark = true,
        useSkeleton = true,
        titlePadding = null,
        leadingPadding = null,
        actionsPadding = null;

  /// Creates a fully customizable AppBar
  const UnifiedAppBar.custom({
    super.key,
    this.title,
    this.subtitle,
    this.leading,
    this.actions,
    this.centerTitle = true,
    this.backgroundColor,
    this.toolbarHeight,
    this.onLeadingTap,
    this.onQuestionTap,
    this.showQuestionMark = true,
    this.useSkeleton = true,
    this.titlePadding,
    this.leadingPadding,
    this.actionsPadding,
  })  : userName = null,
        userHandle = null,
        userProfilePicture = null,
        isOpenToClubInvitation = false,
        userOwnProfile = false,
        bookName = null,
        clubName = null,
        impromptuCount = null,
        showImpromptu = false,
        clubNameFlag = false,
        clubMembers = null,
        onTitleTap = null,
        showBackButton = false,
        showLogo = false,
        backRoute = null,
        searchBookName = null,
        searchAuthorName = null,
        showSearchResults = false;

  @override
  State<UnifiedAppBar> createState() => _UnifiedAppBarState();

  @override
  Size get preferredSize => Size.fromHeight(toolbarHeight ?? kToolbarHeight);
}

class _UnifiedAppBarState extends State<UnifiedAppBar> {
  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: widget.backgroundColor ?? AppConstants.textGreenColor,
      centerTitle: widget.centerTitle,
      toolbarHeight: widget.toolbarHeight,
      leading: _buildLeading(),
      title: _buildTitle(),
      actions: _buildActions(),
    );
  }

  Widget? _buildLeading() {
    final padding =
        widget.leadingPadding ?? const EdgeInsets.only(left: 20.0, top: 10);

    if (widget.leading != null) {
      return Padding(padding: padding, child: widget.leading!);
    }

    if (widget.showBackButton) {
      return Padding(
        padding: EdgeInsets.only(left: 20.0),
        child: NetworkAwareTap(
          onTap: widget.onLeadingTap ?? () => context.pop(),
          child: SvgPicture.asset(
            "assets/icons/svg/Back.svg",
            width: 73,
            height: 65,
            fit: BoxFit.contain,
          ),
        ),
      );
    }

    if (widget.showLogo) {
      return Padding(
        padding: padding,
        child: _buildSkeletonWrapper(
          child: Image.asset(
            "assets/images/eljunto_logo_dark_teal.png",
            fit: BoxFit.cover,
            filterQuality: FilterQuality.high,
            alignment: Alignment.center,
          ),
        ),
      );
    }

    return null;
  }

  Widget? _buildTitle() {
    final padding = widget.titlePadding ?? const EdgeInsets.only(top: 10.0);

    // Profile title with user info
    if (widget.userName != null) {
      return Padding(
        padding: padding,
        child: _buildProfileTitle(),
      );
    }

    // Message title with complex content
    if (widget.bookName != null) {
      return Padding(
        padding: padding,
        child: _buildMessageTitle(),
      );
    }

    // Simple title
    if (widget.title != null) {
      return _buildSimpleTitle();
    }

    return null;
  }

  Widget _buildProfileTitle() {
    String image = ApiConstants.imageBaseUrl;
    String fullImageUrl = image + (widget.userProfilePicture ?? '');

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(50),
          child: CustomCachedNetworkImage(
            imageUrl: fullImageUrl,
            width: 45,
            height: 45,
            errorImage: AppConstants.profileLogoImagePath,
          ),
        ),
        const SizedBox(width: 8),
        Flexible(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.5,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                MarqueeList(
                  children: [
                    Text(
                      widget.userName ?? '',
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      style: lbBold.copyWith(fontSize: 18),
                    ),
                  ],
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Flexible(
                      child: Text(
                        widget.userHandle ?? '',
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        style: lbBold.copyWith(fontSize: 14),
                      ),
                    ),
                    const SizedBox(width: 8),
                    if (widget.isOpenToClubInvitation)
                      Image.asset(
                        AppConstants.openToInvitationImagePath,
                        height: 15,
                        width: 15,
                      )
                    else if (!widget.isOpenToClubInvitation &&
                        widget.userOwnProfile)
                      Image.asset(
                        AppConstants.notOpenToInvitationImagePath,
                        height: 20,
                        width: 20,
                      )
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMessageTitle() {
    return Column(
      children: [
        MarqueeList(
          children: [
            if (widget.onTitleTap != null)
              NetworkAwareTap(
                onTap: widget.onTitleTap!,
                child: Text(
                  widget.bookName ?? '',
                  overflow: TextOverflow.ellipsis,
                  style: lbBold.copyWith(fontSize: 18),
                ),
              )
            else
              Text(
                widget.bookName ?? '',
                overflow: TextOverflow.ellipsis,
                style: lbBold.copyWith(fontSize: 18),
              ),
          ],
        ),
        const SizedBox(height: 5),
        if (widget.clubMembers != null)
          MarqueeList(
            children: widget.clubMembers!.asMap().entries.map(
              (entry) {
                final isLast = entry.key == (widget.clubMembers!.length - 1);
                final name = entry.value.userName;
                return Text(
                  name?.isNotEmpty ?? false ? '$name${isLast ? '' : ', '}' : '',
                  overflow: TextOverflow.ellipsis,
                  style: lbBold.copyWith(fontSize: 10),
                );
              },
            ).toList(),
          ),
        const SizedBox(height: 2),
        if (widget.showImpromptu)
          Text(
            widget.impromptuCount ?? '',
            overflow: TextOverflow.ellipsis,
            style: lbItalic.copyWith(fontSize: 14),
          ),
        if (widget.clubNameFlag)
          MarqueeList(
            children: [
              Text(
                "(${widget.clubName ?? ''})",
                overflow: TextOverflow.ellipsis,
                style: lbBold.copyWith(fontSize: 12),
              ),
            ],
          ),
      ],
    );
  }

  Widget _buildSimpleTitle() {
    return Column(
      children: [
        MarqueeList(
          children: [
            Text(
              widget.title ?? '',
              overflow: TextOverflow.ellipsis,
              style: lbBold.copyWith(fontSize: 18),
            ),
          ],
        ),
        if (widget.subtitle != null) ...[
          const SizedBox(height: 2),
          MarqueeList(
            children: [
              Text(
                widget.subtitle!,
                overflow: TextOverflow.ellipsis,
                style: lbRegular.copyWith(fontSize: 14),
              ),
            ],
          ),
        ],
        if (widget.showSearchResults) ...[
          const SizedBox(height: 2),
          MarqueeList(
            children: [
              Row(
                children: [
                  if (widget.searchBookName?.isNotEmpty ?? false)
                    Text(
                      "${widget.searchBookName ?? ''}, ",
                      overflow: TextOverflow.ellipsis,
                      style: lbRegular.copyWith(fontSize: 14),
                    ),
                  Text(
                    widget.searchAuthorName ?? '',
                    overflow: TextOverflow.ellipsis,
                    style: lbRegular.copyWith(fontSize: 14),
                  )
                ],
              ),
            ],
          ),
        ],
        if (widget.impromptuCount?.isNotEmpty ?? false) ...[
          const SizedBox(height: 2),
          Text(
            widget.impromptuCount ?? '',
            overflow: TextOverflow.ellipsis,
            style: lbItalic.copyWith(fontSize: 14),
          ),
        ],
        if (widget.clubNameFlag) ...[
          const SizedBox(height: 2),
          MarqueeList(
            children: [
              Text(
                "(${widget.clubName ?? ''})",
                overflow: TextOverflow.ellipsis,
                style: lbBold.copyWith(fontSize: 12),
              ),
            ],
          ),
        ],
      ],
    );
  }

  List<Widget> _buildActions() {
    final actions = <Widget>[];
    final padding =
        widget.actionsPadding ?? const EdgeInsets.only(right: 20.0, top: 10);

    // Add custom actions
    if (widget.actions != null) {
      actions.addAll(widget.actions!);
    }

    // Add question mark action
    if (widget.showQuestionMark) {
      actions.add(
        Padding(
          padding: EdgeInsets.only(right: 20.0),
          child: NetworkAwareTap(
            onTap: widget.onQuestionTap ?? _showQuestionDialog,
            child: _buildSkeletonWrapper(
              child: Image.asset(
                AppConstants.questionLogoImagePath,
                height: 34,
                width: 34,
              ),
            ),
          ),
        ),
      );
    }

    return actions;
  }

  Widget _buildSkeletonWrapper({required Widget child}) {
    if (!widget.useSkeleton) return child;

    return Skeleton.replace(
      replacement: ClipRRect(
        borderRadius: BorderRadius.circular(49),
        child: child,
      ),
      child: child,
    );
  }

  void _showQuestionDialog() {
    showDialog(
      context: context,
      barrierColor: Colors.white60,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const QueryFeedbackDialog();
      },
    );
  }
}
