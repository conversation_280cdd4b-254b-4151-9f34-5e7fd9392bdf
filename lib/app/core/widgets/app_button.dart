import 'package:flutter/material.dart';

import '../constants.dart';
import '../utils/text_style.dart';

enum AppButtonStyle {
  solid,
  outlined,
  destructive,
}

class AppButton extends StatelessWidget {
  final String text;
  final double textSize;
  final VoidCallback? onPressed;
  final AppButtonStyle style;
  final bool isLoading;
  final bool isEnabled;
  final double? width;
  final double height;
  final double borderRadius;
  final Color? textColor;
  final Color? destructiveColor;
  final Widget? child;
  final EdgeInsetsGeometry? padding;
  final double borderWidth;
  final Duration animationDuration;

  const AppButton({
    super.key,
    required this.text,
    this.textSize = 16,
    this.onPressed,
    this.style = AppButtonStyle.solid,
    this.isLoading = false,
    this.isEnabled = true,
    this.width,
    this.height = 50,
    this.borderRadius = 30,
    this.textColor,
    this.destructiveColor,
    this.child,
    this.padding,
    this.borderWidth = 1,
    this.animationDuration = const Duration(milliseconds: 250),
  });

  @override
  Widget build(BuildContext context) {
    final canPress = isEnabled && !isLoading && onPressed != null;
    final isOutlined =
        style == AppButtonStyle.outlined || style == AppButtonStyle.destructive;

    final primaryColorForStyle = (style == AppButtonStyle.destructive)
        ? (destructiveColor ?? AppConstants.redColor)
        : AppConstants.textGreenColor;

    final borderColor = isOutlined
        ? style == AppButtonStyle.destructive
            ? (destructiveColor ?? AppConstants.redColor)
            : AppConstants.primaryColor
        : Colors.transparent;

    final finalTextColor = textColor ??
        (isOutlined
            ? (canPress
                ? primaryColorForStyle
                : primaryColorForStyle.withValues(alpha: 0.4))
            : AppConstants.primaryColor);

    final double targetWidth =
        isLoading ? height : (width ?? MediaQuery.of(context).size.width);
    final BorderRadius targetRadius =
        BorderRadius.circular(isLoading ? height / 2 : borderRadius);

    return Center(
      child: AnimatedContainer(
        duration: animationDuration,
        width: targetWidth,
        height: height,
        decoration: BoxDecoration(
          borderRadius: targetRadius,
          gradient: (style == AppButtonStyle.solid && canPress)
              ? LinearGradient(
                  colors: [primaryColorForStyle, primaryColorForStyle],
                )
              : null,
          color: canPress
              ? style == AppButtonStyle.solid
                  ? primaryColorForStyle
                  : Colors.transparent
              : isLoading
                  ? AppConstants.textGreenColor
                  : Colors.grey,
          border: isOutlined
              ? Border.all(
                  color: borderColor,
                  width: borderWidth,
                )
              : null,
        ),
        child: ElevatedButton(
          onPressed: canPress ? onPressed : null,
          style: ElevatedButton.styleFrom(
            padding: EdgeInsets.zero,
            shape: RoundedRectangleBorder(borderRadius: targetRadius),
            backgroundColor: Colors.transparent,
            shadowColor: Colors.transparent,
            elevation: 0,
          ),
          child: AnimatedSwitcher(
            duration: animationDuration,
            transitionBuilder: (child, animation) {
              return FadeTransition(opacity: animation, child: child);
            },
            child: isLoading
                ? SizedBox(
                    key: ValueKey('loader'),
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : child ??
                    Text(
                      key: const ValueKey('text'),
                      text,
                      style: lbBold.copyWith(
                        fontSize: textSize,
                        color: finalTextColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
          ),
        ),
      ),
    );
  }
}
