import 'package:eljunto/app/core/widgets/app_button.dart';
import 'package:eljunto/app/core/widgets/configurable_popup.dart';
import 'package:eljunto/controller/user_controller.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../services/session_manager.dart';
import '../services/setup_locator.dart';

class QueryFeedbackDialog extends StatelessWidget {
  final bool isQuery;

  const QueryFeedbackDialog({super.key, this.isQuery = false});

  @override
  Widget build(BuildContext context) {
    final feedbackController = TextEditingController();
    final sessionManager = locator<SessionManager>();
    final dialogTitle = isQuery ? "Can’t find a book?" : "Questions & Feedback";
    final userId = sessionManager.userId;

    void showSuccessDialog() {
      showDialog(
        context: context,
        builder: (BuildContext context) => ConfigurablePopupDialog(
          bodyText1: dialogTitle,
          bodyText2: "Thanks!\nWe’ll get back to you ASAP!",
          text1FontSize: 16,
          text2FontSize: 14,
          buttons: [
            PopupButton(
              text: 'OK',
              height: 40,
              borderRadius: 36,
              onPressed: () => context.pop(),
            ),
          ],
        ),
      );
    }

    void handleFeedbackSubmit() async {
      final feedback = feedbackController.text;
      final payload = isQuery
          ? {
              "userId": userId,
              "bookFeedback": feedback,
            }
          : {
              "userId": userId,
              "questionOrFeedback": feedback,
            };

      final success =
          await context.read<UserController>().postQuestionFeedback(payload);
      if (success) {
        context.pop();
        showSuccessDialog();
      }
    }

    return Consumer<UserController>(builder: (context, provider, _) {
      return ConfigurablePopupDialog(
        bodyText1: dialogTitle,
        bodyText2: isQuery
            ? "Kindly provide the title and author. We'll get it in here lickety-split."
            : "Questions? Other feedback? Please let us know! We'll get back to you ASAP.",
        text1FontSize: 16,
        text2FontSize: 14,
        textArea: PopupField(
          controller: feedbackController,
          keyboardType: TextInputType.multiline,
          maxLines: 5,
          maxLength: 2000,
        ),
        buttons: [
          PopupButton(
            text: 'Submit',
            height: 40,
            borderRadius: 36,
            isLoading: provider.isUserLoading,
            onPressed: () => handleFeedbackSubmit,
          ),
          PopupButton(
            text: 'Cancel',
            height: 40,
            borderRadius: 36,
            style: AppButtonStyle.outlined,
            onPressed: () => context.pop(),
          ),
        ],
      );
    });
  }
}
