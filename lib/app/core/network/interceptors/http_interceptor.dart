import 'package:dio/dio.dart';
import 'package:eljunto/app/core/routes/app_navigation_keys.dart';
import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:go_router/go_router.dart';

class HttpInterceptor extends Interceptor {
  final List<String> excludedEndpoints = [
    '/auth/signin',
    '/auth/email-verification',
    '/auth/pass-code-verify',
    '/auth/sign-up-set-password',
    '/auth/signout',
    '/error-logs/add',
  ];
  final List<String> profileSetUpEndPoints = [
    'auth/check-available-handle',
    'auth/signup-name-handle'
  ];

  @override
  Future<void> onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    final sessionManager = locator<SessionManager>();
    bool shouldExclude =
        excludedEndpoints.any((endpoint) => options.path.contains(endpoint));
    bool isProfileSetUpEndPoint = profileSetUpEndPoints
        .any((endpoint) => options.path.contains(endpoint));

    if (!shouldExclude || isProfileSetUpEndPoint) {
      final token = sessionManager.token;

      if (token != null) {
        options.headers['Authorization'] = 'Bearer $token';
      }
    }

    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    final code = err.response?.statusCode;
    if (code == 501 || code == 502 || code == 503 || code == 504) {
      AppNavigationKeys.rootNavigatorKey.currentContext?.go('/server-down');
    }
    handler.next(err);
  }
}
