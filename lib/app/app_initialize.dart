import 'dart:developer';

import 'package:eljunto/app/core/helpers/database_helper.dart';
import 'package:eljunto/app/core/providers/app_version_provider.dart';
import 'package:eljunto/app/core/providers/connectivity_provider.dart';
import 'package:eljunto/app/core/services/notification_service.dart';
import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/app/core/utils/app_config.dart';
import 'package:eljunto/app/features/authentication/providers/auth_provider.dart'
    as new_auth;
import 'package:eljunto/app/features/settings/providers/settings_provider.dart';
import 'package:eljunto/app/features/splash/providers/splash_provider.dart';
import 'package:eljunto/app/features/subscription/providers/subscription_provider.dart';
import 'package:eljunto/controller/book_case_controller.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/controller/inapp_purchase_controller.dart';
import 'package:eljunto/controller/leader_admin_controller.dart';
import 'package:eljunto/controller/login_controller.dart';
import 'package:eljunto/controller/message_controller.dart';
import 'package:eljunto/controller/notification_controller.dart';
import 'package:eljunto/controller/profile_controller.dart';
import 'package:eljunto/controller/search_controller.dart';
import 'package:eljunto/controller/subscription_controller.dart';
import 'package:eljunto/controller/user_controller.dart';
import 'package:eljunto/controller/user_credential_controller.dart';
import 'package:eljunto/reusable_api_function/club/club_function.dart';
import 'package:eljunto/views/clubs/clubs_home/providers/clubs_home_provider.dart';
import 'package:eljunto/views/clubs/user_club_details/providers/user_club_details_provider.dart';
import 'package:eljunto/views/home_screen/club_details/providers/club_details_provider.dart';
import 'package:eljunto/views/login/provider/auth_provider.dart';
import 'package:eljunto/views/profile/profile_home/providers/profile_home_provider.dart';
import 'package:eljunto/views/settings/provider/setting_provider.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'features/subscription/services/subscription_service.dart';

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  NotificationServices().notificationCount++;
  NotificationServices().initializeBadgeCount();
  // Check if the notification contains deep link data
  if (message.data.containsKey('deepLink')) {
    try {
      final deepLinkUri = Uri.parse(message.data['deepLink']);
      final prefs = await SharedPreferences.getInstance();
      prefs.setString('pendingDeepLink', deepLinkUri.toString());
    } catch (e) {
      log("Error parsing deep link: $e");
    }
  }
}

class AppInit {
  static final _providers = [
    ChangeNotifierProvider(
      create: (context) => UserCredentialController(),
    ),
    ChangeNotifierProvider(
      create: (context) => LoginController(),
    ),
    ChangeNotifierProvider(
      create: (context) => UserController(),
    ),
    ChangeNotifierProvider(
      create: (context) => BookClubController(),
    ),
    ChangeNotifierProvider(
      create: (context) => ProfileController(),
    ),
    ChangeNotifierProvider(
      create: (context) => BookCaseController(),
    ),
    ChangeNotifierProvider(
      create: (context) => LeaderAdminController(),
    ),
    ChangeNotifierProvider(
      create: (context) => ClubController(),
    ),
    ChangeNotifierProvider(
      create: (context) => SearchDataController(),
    ),
    ChangeNotifierProvider(
      create: (context) => MessageController(),
    ),
    ChangeNotifierProvider(
      create: (context) => NotificationController(),
    ),
    ChangeNotifierProvider(
      create: (context) => SubscriptionController(),
    ),
    ChangeNotifierProvider(
      create: (context) => InAppPurchaseController(),
    ),
    ChangeNotifierProvider.value(
      value: locator<ConnectivityProvider>(),
    ),
    ChangeNotifierProvider(
      create: (context) => AppVersionProvider(),
    ),
    ChangeNotifierProvider(
      create: (context) => UserClubDetailsProvider(),
    ),
    ChangeNotifierProvider(
      create: (context) => ClubsHomeProvider(),
    ),
    ChangeNotifierProvider(
      create: (context) => ClubDetailsProvider(),
    ),
    ChangeNotifierProvider(
      create: (context) => ProfileHomeProvider(),
    ),
    ChangeNotifierProvider(
      create: (context) => AuthProvider(),
    ),
    ChangeNotifierProvider(
      create: (context) => SettingProvider(),
    ),
    ChangeNotifierProxyProvider<ClubController, MessageController>(
      create: (_) => MessageController(),
      update: (_, clubController, messageController) =>
          messageController!..updateClubNotifications(clubController),
    ),

    // New and improved providers
    ChangeNotifierProvider(
      create: (context) => SettingsProvider(),
    ),
    // New consolidated authentication provider
    ChangeNotifierProvider(
      create: (context) => new_auth.AuthProvider(),
    ),
    ChangeNotifierProvider(
      create: (context) => SubscriptionProvider(SubscriptionService()),
    ),
    ChangeNotifierProvider.value(
      value: locator<SessionManager>(),
    ),
    ChangeNotifierProvider(
      create: (context) => SplashProvider(),
    ),
  ];

  static List<SingleChildWidget> get providers => _providers;

  /// Initializes the app with the given [appName], [baseUrl], [flavor],
  /// [firebaseName], and [firebaseOptions].
  ///
  /// This function:
  /// 1. Initializes [AppConfig] with the given parameters.
  /// 2. Ensures the [WidgetsFlutterBinding] is initialized.
  /// 3. Initializes Firebase with the given [firebaseName] and [firebaseOptions].
  /// 4. Sets up the service locator.
  /// 5. Initializes the local database.
  /// 6. Initializes the notification service and sets up the badge count.
  /// 7. Sets up the background message handler for Firebase Cloud Messaging.
  /// If any of these steps fail, it logs the error and re-throws the exception.
  static Future<void> initializeApp({
    required String appName,
    required String baseUrl,
    required Flavor flavor,
    required String firebaseName,
    required FirebaseOptions firebaseOptions,
  }) async {
    try {
      AppConfig(appName: appName, baseUrl: baseUrl, flavor: flavor);
      WidgetsFlutterBinding.ensureInitialized();
      await Firebase.initializeApp(
          name: firebaseName, options: firebaseOptions);
      setupLocator();
      DatabaseHelper.instance;
      NotificationServices().initializeBadgeCount();
      FirebaseMessaging.onBackgroundMessage(
          _firebaseMessagingBackgroundHandler);
    } catch (e) {
      log("Failed to initialize app: $e");
      rethrow; // Re-throw to allow main() to handle the error
    }
  }
}
