import 'dart:developer';

import 'package:eljunto/app/core/utils/system_utils.dart';
import 'package:eljunto/app/core/widgets/version_display.dart';
import 'package:eljunto/reusableWidgets/connection_error/no_connection_tag.dart';
import 'package:eljunto/reusableWidgets/contact_us.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../reusableWidgets/custom_text_widget.dart';
import '../../app/core/constants.dart';
import '../../app/core/services/notification_service.dart';
import '../../app/core/utils/text_style.dart';
import '../../reusableWidgets/custom_button.dart';
import 'provider/auth_provider.dart';

class SetPasswordPage extends StatefulWidget {
  final String email;
  final String token;
  final String otp;
  final bool? isForgotPassword;

  const SetPasswordPage({
    super.key,
    required this.email,
    required this.token,
    required this.otp,
    this.isForgotPassword,
  });

  @override
  State createState() => _SetPasswordPageState();
}

class _SetPasswordPageState extends State<SetPasswordPage> {
  final _passwordFormKey = GlobalKey<FormState>();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();

  final FocusNode _passwordFocusNode = FocusNode();
  final FocusNode _confirmPasswordFocusNode = FocusNode();

  bool showPassword = true;
  bool showConfirmPassword = true;
  String? appName, appVersion;
  NotificationServices notificationServices = NotificationServices();
  String? deviceId;
  late AuthProvider provider;

  @override
  void initState() {
    provider = Provider.of<AuthProvider>(context, listen: false);
    provider.initialize(context);
    notificationServices.getDeviceToken().then((value) async {
      if (value.isNotEmpty) {
        fcmToken = value;
      } else {
        fcmToken = await notificationServices.getDeviceToken();
      }
    });
    log('FCM Token : $fcmToken');
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      deviceId = await SystemUtils.getDeviceName();
    });

    super.initState();
  }

  String? fcmToken;

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _passwordFocusNode.dispose();
    _confirmPasswordFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.cover,
          ),
        ),
        child: PopScope(
          canPop: false,
          child: Stack(
            children: [
              Scaffold(
                backgroundColor: Colors.transparent,
                appBar: AppBar(
                  toolbarHeight: 140,
                  shape: const Border(
                    bottom: BorderSide(
                      color: AppConstants.primaryColor,
                      width: 1.5,
                    ),
                  ),
                  automaticallyImplyLeading: false,
                  backgroundColor: AppConstants.textGreenColor,
                  centerTitle: true,
                  title: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        AppConstants.elJuntoLogo,
                        height: 100,
                        width: 80,
                        filterQuality: FilterQuality.high,
                        fit: BoxFit.contain,
                      ),
                      const VersionDisplay(),
                      const SizedBox(height: 25),
                    ],
                  ),
                ),
                body: ListView(
                  children: <Widget>[
                    const SizedBox(
                      height: 25,
                    ),
                    Text(
                      widget.isForgotPassword ?? false
                          ? 'Reset Password'
                          : 'Create New Account',
                      style: lbRegular.copyWith(
                        fontSize: 24,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    Form(
                      key: _passwordFormKey,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Consumer<AuthProvider>(
                          builder: (context, authProvider, child) {
                            return Column(
                              children: [
                                const SizedBox(height: 25),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Text(
                                      widget.isForgotPassword ?? false
                                          ? 'New password:'
                                          : "Set Password:",
                                      style: lbRegular.copyWith(
                                        fontSize: 18,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                SizedBox(
                                  child: TextFormField(
                                    controller: _passwordController,
                                    focusNode: _passwordFocusNode,
                                    style: lbRegular.copyWith(
                                      fontSize: 18,
                                    ),
                                    onChanged: (value) {
                                      authProvider.validatePassword(
                                        _passwordController.text.trim(),
                                        _confirmPasswordController.text.trim(),
                                      );
                                    },
                                    decoration: InputDecoration(
                                      contentPadding: const EdgeInsets.all(10),
                                      filled: true,
                                      fillColor:
                                          Colors.white.withValues(alpha: 0.8),
                                      errorStyle: errorMsg,
                                      border: const OutlineInputBorder(),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                        borderSide: const BorderSide(
                                          color: AppConstants.primaryColor,
                                          width: 1.5,
                                        ),
                                      ),
                                      disabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                        borderSide: const BorderSide(
                                          color: Colors.black38,
                                          width: 1.5,
                                        ),
                                      ),
                                      suffixIcon: IconButton(
                                        icon: Icon(
                                          showPassword
                                              ? Icons.visibility_off
                                              : Icons.visibility,
                                          color: AppConstants.primaryColor,
                                        ),
                                        onPressed: () {
                                          setState(() {
                                            showPassword = !showPassword;
                                          });
                                        },
                                      ),
                                    ),
                                    enabled: authProvider.isFieldEnabled,
                                    obscureText: showPassword,
                                    // validator: (value) {
                                    //   if (value == null || value.isEmpty) {
                                    //     return 'Please enter your password';
                                    //   }
                                    //   return null;
                                    // },
                                  ),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    authProvider.setPasswordFlag
                                        ? Text(
                                            "*Enter password",
                                            style: errorMsg,
                                          )
                                        : const SizedBox.shrink(),
                                  ],
                                ),
                                _buildPasswordValidationMessages(),
                                const SizedBox(height: 25),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Text(
                                      "Confirm Password:",
                                      style: lbRegular.copyWith(
                                        fontSize: 18,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                SizedBox(
                                  child: TextFormField(
                                    controller: _confirmPasswordController,
                                    style: lbRegular.copyWith(
                                      fontSize: 18,
                                    ),
                                    onChanged: (value) {
                                      authProvider.validateConfirmPassword(
                                        _confirmPasswordController.text.trim(),
                                        _passwordController.text.trim(),
                                      );
                                    },
                                    decoration: InputDecoration(
                                      contentPadding: const EdgeInsets.all(10),
                                      filled: true,
                                      fillColor:
                                          Colors.white.withValues(alpha: 0.8),
                                      errorStyle: errorMsg,
                                      border: const OutlineInputBorder(),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                        borderSide: const BorderSide(
                                          color: AppConstants.primaryColor,
                                          width: 1.5,
                                        ),
                                      ),
                                      disabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                        borderSide: const BorderSide(
                                          color: Colors.black38,
                                          width: 1.5,
                                        ),
                                      ),
                                      suffixIcon: IconButton(
                                        icon: Icon(
                                          showConfirmPassword
                                              ? Icons.visibility_off
                                              : Icons.visibility,
                                          color: AppConstants.primaryColor,
                                        ),
                                        onPressed: () {
                                          setState(() {
                                            showConfirmPassword =
                                                !showConfirmPassword;
                                          });
                                        },
                                      ),
                                    ),
                                    enabled: authProvider.isFieldEnabled,
                                    obscureText: showConfirmPassword,
                                    // validator: (value) {
                                    //   if (value == null || value.isEmpty) {
                                    //     return 'Please enter confirm password';
                                    //   }
                                    //   return null;
                                    // },
                                  ),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    authProvider.confirmPasswordFlag
                                        ? Text(
                                            "*Confirm password",
                                            style: errorMsg,
                                          )
                                        : const SizedBox.shrink(),
                                  ],
                                ),
                                _buildConfirmPasswordValidationMessage(),
                                authProvider.errorMessage.isNotEmpty
                                    ? Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Expanded(
                                            child: Text(
                                              '* ${authProvider.errorMessage}',
                                              style: lbRegular.copyWith(
                                                color: AppConstants.redColor,
                                                fontSize: 14,
                                              ),
                                            ),
                                          ),
                                        ],
                                      )
                                    : SizedBox.shrink(),
                                const SizedBox(height: 25),
                                CustomLoaderButton(
                                  buttonWidth: authProvider.passwordLoading
                                      ? 45.0
                                      : MediaQuery.of(context).size.width,
                                  buttonRadius: 30.0,
                                  buttonChild: authProvider.passwordLoading
                                      ? const CircularProgressIndicator(
                                          valueColor: AlwaysStoppedAnimation(
                                              Colors.white),
                                          strokeWidth: 3.0,
                                        )
                                      : Text(
                                          'Next',
                                          style: lbBold.copyWith(
                                            fontSize: 18,
                                            color: AppConstants.primaryColor,
                                          ),
                                        ),
                                  buttonPressed: () {
                                    widget.isForgotPassword ?? false
                                        ? authProvider.changePassword(
                                            _passwordController.text.trim(),
                                            _confirmPasswordController.text
                                                .trim(),
                                            widget.email,
                                            widget.otp,
                                            widget.token,
                                            deviceId ?? '',
                                            _passwordFormKey,
                                            context,
                                          )
                                        : authProvider.setPassword(
                                            _passwordController.text.trim(),
                                            _confirmPasswordController.text
                                                .trim(),
                                            widget.email,
                                            widget.otp,
                                            widget.token,
                                            deviceId ?? '',
                                            _passwordFormKey,
                                            context,
                                          );
                                  },
                                ),
                              ],
                            );
                          },
                        ),
                      ),
                    ),
                    const SizedBox(height: 25),
                    const ContactUs(),
                  ],
                ),
              ),
              NoConnectionTag(bottomPosition: 70),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildValidationMessage(
      String message, bool isValid, bool shouldShow) {
    if (!shouldShow) {
      return const SizedBox.shrink();
    }
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          isValid ? Icons.check_circle : Icons.error,
          color: isValid ? Colors.green : Colors.red,
          size: 18,
        ),
        const SizedBox(width: 8.0),
        Expanded(
          child: Text(message, style: lbItalic),
        ),
      ],
    );
  }

  Widget _buildPasswordValidationMessages() {
    return Consumer<AuthProvider>(builder: (context, authProvider, child) {
      return Column(
        children: [
          const SizedBox(
            height: 10,
          ),
          _buildValidationMessage(
            '8 Characters',
            authProvider.isPasswordValid,
            // _passwordController.text.isNotEmpty,
            true,
          ),
          const SizedBox(
            height: 10,
          ),
          _buildValidationMessage(
            '3 of the following: Capital letter, lowercase letter, number or symbol',
            authProvider.isPasswordComplex,
            // _passwordController.text.isNotEmpty,
            true,
          ),
        ],
      );
    });
  }

  Widget _buildConfirmPasswordValidationMessage() {
    return Consumer<AuthProvider>(builder: (context, authProvider, child) {
      return Column(
        children: authProvider.isPasswordMatch
            ? [
                const SizedBox(
                  height: 10,
                ),
                _buildValidationMessage('Matches', authProvider.isPasswordMatch,
                    _confirmPasswordController.text.isNotEmpty)
              ]
            : [
                const SizedBox(
                  height: 10,
                ),
                _buildValidationMessage(
                    'Password does not match',
                    authProvider.isPasswordMatch,
                    _confirmPasswordController.text.isNotEmpty)
              ],
      );
    });
  }
}
