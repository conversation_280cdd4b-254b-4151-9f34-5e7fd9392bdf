import 'dart:async';
import 'dart:developer';

import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/enums/enums.dart';
import 'package:eljunto/app/core/providers/connectivity_provider.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/views/meeting/mixins/base_video_call_mixin.dart';
import 'package:eljunto/views/meeting/mixins/call_initialization_mixin.dart';
import 'package:eljunto/views/meeting/mixins/call_manager_listener_mixin.dart';
import 'package:eljunto/views/meeting/mixins/permission_handler_mixin.dart';
import 'package:eljunto/views/meeting/mixins/video_quality_mixin.dart';
import 'package:eljunto/views/meeting/services/overlay_service.dart';
import 'package:eljunto/views/meeting/services/video_call_manager.dart';
import 'package:eljunto/views/meeting/widgets/call_failed_view.dart';
import 'package:eljunto/views/meeting/widgets/control_panel.dart';
import 'package:eljunto/views/meeting/widgets/discussion_sheet.dart';
import 'package:eljunto/views/meeting/widgets/leave_call_dialog.dart';
import 'package:eljunto/views/meeting/widgets/loading_indicator.dart';
import 'package:eljunto/views/meeting/widgets/video_call_app_bar.dart';
import 'package:eljunto/views/meeting/widgets/video_grid_layout.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import '../../app/core/utils/system_utils.dart';

abstract class MeetingScreenStateAccessor {
  bool get isLoading;
  set isLoading(bool value);

  bool get callJoinedSuccessfully;
  set callJoinedSuccessfully(bool value);

  bool get buttonsVisible;
  set buttonsVisible(bool value);

  bool get isExitingForPipMode;
  set isExitingForPipMode(bool value);

  Map<int, String> get userHandlesMap;
  Map<int, String> get userProfilePictureMap;
  Map<int, VideoViewController> get screenRemoteControllers;
  RtcEngine? get engine;

  set engine(RtcEngine? value);

  DateTime get lastQualityAdjustment;
  set lastQualityAdjustment(DateTime value);

  int get poorQualityCounter;
  set poorQualityCounter(int value);

  int get goodQualityCounter;
  set goodQualityCounter(int value);

  Duration get debounceInterval;
  double get controlPanelHeight;
  double get bottomSheetPeekHeight;

  void syncScreenControllers(Set<int> currentRemoteUids);

  Future<void> onCallEnd(
      {bool userInitiated = false, bool remoteInitiated = false});

  void addNetworkQualityListener();
  void updateStateFromManager();
}

// ignore: must_be_immutable
class MeetingScreen extends StatefulWidget
    implements MeetingScreenStateAccessor {
  final int bookClubId;
  final String? bookName;
  final String? token;
  final int? userId;
  final String? discussionQue;
  final String? channelName;
  final String? userHandle;
  final String? profilePictureUrl;
  final bool isRejoin;

  MeetingScreen({
    super.key,
    required this.bookClubId,
    this.bookName,
    this.token,
    this.userId,
    this.discussionQue,
    this.channelName,
    this.userHandle,
    this.profilePictureUrl,
    this.isRejoin = false,
  });

  @override
  State<MeetingScreen> createState() => _MeetingScreenState();

  @override
  bool get isLoading => (_state?._isLoading) ?? true;

  @override
  set isLoading(bool value) {
    if (_state != null) _state!._isLoading = value;
  }

  @override
  bool get callJoinedSuccessfully => (_state?._callJoinedSuccessfully) ?? false;

  @override
  set callJoinedSuccessfully(bool value) {
    if (_state != null) _state!._callJoinedSuccessfully = value;
  }

  @override
  bool get buttonsVisible => (_state?._buttonsVisible) ?? true;

  @override
  set buttonsVisible(bool value) {
    if (_state != null) _state!._buttonsVisible = value;
  }

  @override
  bool get isExitingForPipMode => (_state?._isExitingForPipMode) ?? false;

  @override
  set isExitingForPipMode(bool value) {
    if (_state != null) _state!._isExitingForPipMode = value;
  }

  @override
  Map<int, String> get userHandlesMap => _state?.userHandlesMap ?? {};

  @override
  Map<int, String> get userProfilePictureMap =>
      _state?.userProfilePictureMap ?? {};

  @override
  Map<int, VideoViewController> get screenRemoteControllers =>
      _state?.screenRemoteControllers ?? {};

  @override
  RtcEngine? get engine => _state?._engine;

  @override
  set engine(RtcEngine? value) {
    if (_state != null) _state!._engine = value;
  }

  @override
  DateTime get lastQualityAdjustment =>
      _state?._lastQualityAdjustment ?? DateTime.now();

  @override
  set lastQualityAdjustment(DateTime value) {
    if (_state != null) _state!._lastQualityAdjustment = value;
  }

  @override
  int get poorQualityCounter => (_state?._poorQualityCounter) ?? 0;

  @override
  set poorQualityCounter(int value) {
    if (_state != null) _state!._poorQualityCounter = value;
  }

  @override
  int get goodQualityCounter => (_state?._goodQualityCounter) ?? 0;

  @override
  set goodQualityCounter(int value) {
    if (_state != null) _state!._goodQualityCounter = value;
  }

  @override
  Duration get debounceInterval => const Duration(seconds: 3);

  @override
  double get controlPanelHeight => 80.0;

  @override
  double get bottomSheetPeekHeight => 80.0;

  @override
  void syncScreenControllers(Set<int> currentRemoteUids) {
    _state?.syncScreenControllers(currentRemoteUids);
  }

  @override
  Future<void> onCallEnd(
      {bool userInitiated = false, bool remoteInitiated = false}) async {
    await _state?.onCallEnd(
        userInitiated: userInitiated, remoteInitiated: remoteInitiated);
  }

  @override
  void addNetworkQualityListener() {
    _state?.addNetworkQualityListener();
  }

  @override
  void updateStateFromManager() {
    _state?.updateStateFromManager();
  }

  _MeetingScreenState? _state;
}

class _MeetingScreenState extends State<MeetingScreen>
    with
        BaseVideoCallMixin<MeetingScreen>,
        CallInitializationMixin,
        CallManagerListenerMixin,
        PermissionHandlerMixin,
        VideoQualityMixin,
        WidgetsBindingObserver
    implements MeetingScreenStateAccessor {
  final _videoCallManager = VideoCallManager();

  @override
  VideoCallManager get videoCallManager => _videoCallManager;

  @override
  RtcEngine? get engine => _engine;
  RtcEngine? _engine;

  @override
  set engine(RtcEngine? value) => _engine = value;

  @override
  bool get isLoading => _isLoading;
  bool _isLoading = true;

  @override
  set isLoading(bool value) => _isLoading = value;

  @override
  bool get callJoinedSuccessfully => _callJoinedSuccessfully;
  bool _callJoinedSuccessfully = false;

  @override
  set callJoinedSuccessfully(bool value) => _callJoinedSuccessfully = value;

  @override
  bool get buttonsVisible => _buttonsVisible;
  bool _buttonsVisible = true;

  @override
  set buttonsVisible(bool value) => _buttonsVisible = value;

  @override
  bool get isExitingForPipMode => _isExitingForPipMode;
  bool _isExitingForPipMode = false;

  @override
  set isExitingForPipMode(bool value) => _isExitingForPipMode = value;

  @override
  DateTime get lastQualityAdjustment => _lastQualityAdjustment;
  DateTime _lastQualityAdjustment = DateTime.now();

  @override
  set lastQualityAdjustment(DateTime value) => _lastQualityAdjustment = value;

  @override
  int get poorQualityCounter => _poorQualityCounter;
  int _poorQualityCounter = 0;

  @override
  set poorQualityCounter(int value) => _poorQualityCounter = value;

  @override
  int get goodQualityCounter => _goodQualityCounter;
  int _goodQualityCounter = 0;

  @override
  set goodQualityCounter(int value) => _goodQualityCounter = value;

  @override
  Duration get debounceInterval => const Duration(seconds: 3);

  @override
  double get controlPanelHeight => 80.0;

  @override
  double get bottomSheetPeekHeight => 80.0;

  @override
  final Map<int, String> userHandlesMap = <int, String>{};
  @override
  final Map<int, String> userProfilePictureMap = <int, String>{};
  @override
  final Map<int, VideoViewController> screenRemoteControllers = {};

  Timer? _autoHideTimer;

  @override
  BuildContext get buildContext => context;

  @override
  void initState() {
    super.initState();
    widget._state = this;
    _loadInitialData();
    WakelockPlus.enable();
    addManagerListeners();
    initializeCallAndPermissions(this);
    _startAutoHideTimer();

    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    _autoHideTimer?.cancel();
    WakelockPlus.disable();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.paused && videoCallManager.isCallActive) {
      _prepareForPossibleNavigation();
    }
  }

  void _prepareForPossibleNavigation() {
    isExitingForPipMode = true;
    _disposeUnneededControllers();
    OverlayService().prepareVideoCallOverlay();
  }

  void _loadInitialData() async {
    final bookClubController =
        Provider.of<BookClubController>(context, listen: false);
    await bookClubController.getBookClubMembers(widget.bookClubId, context);
    for (final map in bookClubController.userProfilePicture) {
      map.forEach((key, value) {
        if (key != null && value != null) userProfilePictureMap[key] = value;
      });
    }
    for (final map in bookClubController.userHandles) {
      map.forEach((key, value) {
        if (key != null && value != null) userHandlesMap[key] = value;
      });
    }
    if (widget.userId != null && widget.userHandle != null) {
      userHandlesMap[widget.userId!] ??= widget.userHandle!;
    }
    if (widget.userId != null && widget.profilePictureUrl != null) {
      userProfilePictureMap[widget.userId!] ??= widget.profilePictureUrl!;
    }
  }

  @override
  void syncScreenControllers(Set<int> currentRemoteUids) {
    if (!mounted || engine == null) {
      screenRemoteControllers.forEach((_, controller) => controller.dispose());
      screenRemoteControllers.clear();
      return;
    }

    final currentScreenUids = screenRemoteControllers.keys.toSet();
    final uidsToAdd = currentRemoteUids.difference(currentScreenUids);
    final uidsToRemove = currentScreenUids.difference(currentRemoteUids);

    for (int uid in uidsToRemove) {
      screenRemoteControllers.remove(uid)?.dispose();
    }

    final channelName = videoCallManager.getCallMetadata()?['channelName'] ??
        widget.channelName ??
        "";

    for (int uid in uidsToAdd) {
      screenRemoteControllers[uid] = VideoViewController.remote(
        rtcEngine: engine!,
        canvas: VideoCanvas(
          uid: uid,
          renderMode: RenderModeType.renderModeHidden,
          mirrorMode: VideoMirrorModeType.videoMirrorModeDisabled,
        ),
        connection: RtcConnection(channelId: channelName),
        useFlutterTexture: false,
        useAndroidSurfaceView: false,
      );
    }
  }

  void _startAutoHideTimer() {
    _autoHideTimer?.cancel();
    _autoHideTimer = Timer(const Duration(seconds: 5), () {
      if (mounted && buttonsVisible) {
        setState(() {
          buttonsVisible = false;
        });
      }
    });
  }

  void _onScreenTap() {
    if (!mounted) return;
    setState(() {
      buttonsVisible = !buttonsVisible;
    });
    if (buttonsVisible) {
      _startAutoHideTimer();
    } else {
      _autoHideTimer?.cancel();
    }
  }

  Future<void> _toggleAudio() async {
    if (videoCallManager.localAudioMutedNotifier.value) {
      final hasPermission = await checkPermission(Permission.microphone);
      if (!hasPermission) return;
    }
    videoCallManager.toggleAudio();
    _startAutoHideTimer();
  }

  Future<void> _toggleVideo() async {
    if (videoCallManager.localVideoMutedNotifier.value) {
      final hasPermission = await checkPermission(Permission.camera);
      if (!hasPermission) return;
    }
    videoCallManager.toggleVideo();
    _startAutoHideTimer();
  }

  void _toggleCamera() {
    videoCallManager.switchCamera();
    _startAutoHideTimer();
  }

  void _togglePipMode({bool isInitiatedByDialog = false}) {
    if (!videoCallManager.isCallActive) return;

    setState(() => isExitingForPipMode = true);

    _disposeUnneededControllers();

    Future.delayed(const Duration(milliseconds: 100), () {
      OverlayService().showVideoCallOverlay();

      if (mounted) {
        Future.microtask(() => context.pop());
      }
    });
  }

  void _disposeUnneededControllers() {
    final remoteUids = videoCallManager.remoteUsersNotifier.value;
    final uidsToKeep = remoteUids.isEmpty ? [] : [remoteUids.first];

    for (final entry in screenRemoteControllers.entries) {
      if (!uidsToKeep.contains(entry.key)) {
        entry.value.dispose();
      }
    }

    screenRemoteControllers.removeWhere(
      (key, _) => !uidsToKeep.contains(key),
    );
  }

  void _onPop(bool didPop) async {
    if (didPop) return;

    if (isExitingForPipMode) {
      isExitingForPipMode = false;

      if (!OverlayService().isOverlayActive) {
        OverlayService().showVideoCallOverlay();
      }

      if (mounted && context.canPop()) {
        context.pop();
      } else if (mounted) {
        context.go('/');
      }
      return;
    }

    final bool? shouldLeave = await LeaveCallDialog.showConfirmationDialog(
      context: context,
      isCallActive: videoCallManager.isCallActive,
    );
    if (shouldLeave == true) {
      await onCallEnd(userInitiated: true);
      if (mounted && context.canPop()) {
        context.pop();
      }
    } else if (shouldLeave == null) {
      _togglePipMode(isInitiatedByDialog: true);
    }
  }

  @override
  Future<void> onCallEnd(
      {bool userInitiated = false, bool remoteInitiated = false}) async {
    if (isExitingForPipMode) return;
    if (!videoCallManager.isCallActive && !userInitiated && !remoteInitiated) {
      return;
    }

    await videoCallManager.endCall();
    if ((userInitiated || remoteInitiated) && mounted && context.canPop()) {
      try {
        context.pop();
      } catch (e) {
        log('Error popping: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final mode = SystemUtils.getSystemNavigationMode(context);
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) async => _onPop(didPop),
      child: SafeArea(
        bottom: mode == SystemNavigationMode.gesture.value ? false : true,
        top: false,
        child: Scaffold(
          appBar: VideoCallAppBar(
            bookName: widget.bookName ?? '',
            onBackPressed: () => Navigator.of(context).maybePop(),
          ),
          body: Container(
            color: Colors.black,
            child: _buildScreenContent(),
          ),
        ),
      ),
    );
  }

  Widget _buildScreenContent() {
    if (isLoading) {
      return const LoadingIndicator();
    } else if (!callJoinedSuccessfully) {
      return const CallFailedView();
    } else {
      return Column(
        children: [
          Expanded(
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: _onScreenTap,
              child: Stack(
                children: [
                  AnimatedPadding(
                    padding: EdgeInsets.only(
                        bottom: buttonsVisible ? controlPanelHeight : 0.0),
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                    child: Container(
                      color: Colors.black,
                      child: VideoGridLayout(
                        videoCallManager: videoCallManager,
                        engine: engine,
                        screenRemoteControllers: screenRemoteControllers,
                        userHandlesMap: userHandlesMap,
                        userProfilePictureMap: userProfilePictureMap,
                        profilePictureUrl: widget.profilePictureUrl,
                        callJoinedSuccessfully: callJoinedSuccessfully,
                      ),
                    ),
                  ),
                  AnimatedPositioned(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                    bottom: buttonsVisible ? 0 : -controlPanelHeight,
                    left: 0,
                    right: 0,
                    child: ControlPanel(
                      videoCallManager: videoCallManager,
                      onToggleAudio: _toggleAudio,
                      onToggleVideo: _toggleVideo,
                      onToggleCamera: _toggleCamera,
                      onEndCall: () => onCallEnd(userInitiated: true),
                      onTogglePipMode: _togglePipMode,
                      height: controlPanelHeight,
                    ),
                  ),
                  Consumer<ConnectivityProvider>(
                      builder: (context, provider, _) {
                    bool showNoInternetBanner =
                        provider.status == InternetStatus.disconnected;
                    return AnimatedPositioned(
                      duration: const Duration(milliseconds: 300),
                      bottom: showNoInternetBanner ? controlPanelHeight : -150,
                      left: 0,
                      right: 0,
                      child: Center(
                        child: Container(
                          padding: const EdgeInsets.all(5),
                          decoration: BoxDecoration(
                            color: AppConstants.backgroundColor,
                            borderRadius: BorderRadius.circular(5),
                            border:
                                Border.all(color: AppConstants.primaryColor),
                          ),
                          child: Text(
                            'No Internet Connection',
                            style: lbRegular.copyWith(
                              fontSize: 10,
                              color: AppConstants.primaryColor,
                              decoration: TextDecoration.none,
                            ),
                          ),
                        ),
                      ),
                    );
                  }),
                ],
              ),
            ),
          ),
          DiscussionSheet(
            discussionQue: widget.discussionQue,
            peekHeight: bottomSheetPeekHeight,
          ),
        ],
      );
    }
  }
}
