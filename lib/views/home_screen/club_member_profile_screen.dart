import 'dart:developer';

import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/enums/enums.dart';
import 'package:eljunto/app/core/helpers/data_helper.dart';
import 'package:eljunto/app/core/helpers/datetime_helper.dart';
import 'package:eljunto/app/core/services/connectivity_service.dart';
import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/app/core/utils/generic_messages.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/controller/book_case_controller.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/controller/user_controller.dart';
import 'package:eljunto/models/book_case_model.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/models/user_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/customDialouge_with_message.dart';
import 'package:eljunto/reusableWidgets/invite_popup_dialog.dart';
import 'package:eljunto/reusableWidgets/no_data_widget.dart';
import 'package:eljunto/reusableWidgets/profile_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../models/home_model/home_screen4_model/block_account_model.dart';
import '../../reusableWidgets/cached_network_image.dart';
import '../../reusableWidgets/marquee_text.dart';
import '../../reusableWidgets/matches_bottom_sheet/matches_bottom_sheet.dart';
import '../../reusableWidgets/question_feedback_dialog.dart';
import '../../reusable_api_function/club/club_function.dart';

class ClubMemberProfile extends StatefulWidget {
  final int userId;
  final String? userName;
  final String? bookClubId;
  final bool? fromChat;

  const ClubMemberProfile({
    super.key,
    required this.userId,
    this.userName,
    this.bookClubId,
    this.fromChat,
  });

  @override
  State<ClubMemberProfile> createState() => _ClubMemberProfileState();
}

class _ClubMemberProfileState extends State<ClubMemberProfile> {
  bool isTrue = true;
  bool isLoading = false;
  UserModel? userModel;
  int? loggedInUserId;
  List<BookCaseModel> currentlyReadingBooks = [];
  List<BookCaseModel> topShelfBooks = [];
  List<BookCaseModel> completedBooks = [];
  List<BookClubModel>? standingBookClubs;
  List<BookClubModel>? impromptuBookClubs;
  List<BookClubModel> clubsLedByUser = [];
  String? formattedDate;
  bool invitationMsgValidation = false;
  bool bookClubValidation = false;
  bool currentReadLoading = false;
  bool topShelfLoading = false;
  int offset = 0;
  int currentReadLimit = 10;
  int topShelfLimit = 10;
  int currentReadcount = 0;
  int topShelfCount = 0;
  ClubController? clubController;
  BookClubController? bookClubController;

  List<BlockAccountModel> blockList = [
    BlockAccountModel(
      reasons: "Blocked this account.",
    ),
    BlockAccountModel(
      reasons: "Been blocked by this account.",
    ),
  ];
  final ScrollController _currentReadScrollController = ScrollController();
  final ScrollController _topShelfScrollController = ScrollController();
  final ScrollController _standingClubscrollController = ScrollController();
  final ScrollController _impromClubScrollController = ScrollController();
  final ScrollController _toBeReadScrollController = ScrollController();
  UserController? userController;
  BookCaseController? bookCaseController;

  int toBeReadcount = 0;
  int toBeReadLimit = 10;
  bool toBeReadLoading = false;
  List<BookCaseModel>? toBeReadList = [];
  String image = ApiConstants.imageBaseUrl;
  bool isButtonLoading = false;
  bool isDelay = true;

  final _sessionManager = locator<SessionManager>();

  @override
  void initState() {
    Future.delayed(Duration(milliseconds: 500)).then((_) {
      setState(() {
        isDelay = false;
      });
    });
    bookCaseController =
        Provider.of<BookCaseController>(context, listen: false);
    clubController = Provider.of<ClubController>(context, listen: false);
    userController = Provider.of<UserController>(context, listen: false);
    bookClubController =
        Provider.of<BookClubController>(context, listen: false);
    _currentReadScrollController.addListener(_currentReadOnScroll);
    _topShelfScrollController.addListener(_topShelfOnScroll);
    _standingClubscrollController.addListener(_standingOnScroll);
    _impromClubScrollController.addListener(_impromptuOnScroll);
    _toBeReadScrollController.addListener(_toBeReadOnScroll);
    super.initState();
    loadData();
  }

  Future<void> loadData() async {
    setState(() {
      isLoading = true;
    });
    await _initializeUserId();
    await getUserDetails();
    await Future.wait([
      getCurrentReadBookCase(false),
      getTopShelfBookCase(false),
      getToBeReadBook(false),
      getStandingBookClubsByUserId(false),
      getImpromptuBookClubsByUserId(false),
    ]).then((_) {
      setState(() {
        isLoading = false;
      });
    });
    formattedDate =
        DateTimeHelper.getMonthYearDateFormat(userModel?.data?.userCreatedDate);
  }

  // CURRENT READING SCROLL
  void _currentReadOnScroll() {
    if (_currentReadScrollController.position.pixels >=
            _currentReadScrollController.position.maxScrollExtent &&
        !currentReadLoading &&
        (currentlyReadingBooks.length) < currentReadcount) {
      ConnectivityService.networkClose(getCurrentReadBookCase(true), context);
      // getCurrentReadBookCase(true);
      // getFellowReaders(true); // Fetch more data
    }
  }

  // TOPSHELF LIST SCROLL
  void _topShelfOnScroll() {
    if (_topShelfScrollController.position.pixels >=
            _topShelfScrollController.position.maxScrollExtent &&
        !topShelfLoading &&
        (topShelfBooks.length) < topShelfCount) {
      ConnectivityService.networkClose(getTopShelfBookCase(true), context);
      // getTopShelfBookCase(true);
      // getFellowReaders(true); // Fetch more data
    }
  }

  // STANDING CLUB SCROLL
  void _standingOnScroll() {
    if (_standingClubscrollController.position.pixels >=
            _standingClubscrollController.position.maxScrollExtent &&
        (standingLoading == false) &&
        (standingBookClubs?.length ?? 0) < (standingClubCount)) {
      ConnectivityService.networkClose(
          getStandingBookClubsByUserId(true), context);
      // getStandingBookClubsByUserId(true);
    }
  }

  // // IMPROMPTU CLUB SCROLL
  void _impromptuOnScroll() {
    if (_impromClubScrollController.position.pixels >=
            _impromClubScrollController.position.maxScrollExtent &&
        (impromptuLoading == false) &&
        (impromptuBookClubs?.length ?? 0) < (impromptuClubCount)) {
      ConnectivityService.networkClose(
          getImpromptuBookClubsByUserId(true), context);

      // getImpromptuBookClubsByUserId(true);
    }
  }

  /// TO BE READ SCROLL FUNCTION
  void _toBeReadOnScroll() {
    if (_toBeReadScrollController.position.pixels >=
            _toBeReadScrollController.position.maxScrollExtent &&
        !toBeReadLoading &&
        (toBeReadList?.length ?? 0) < (toBeReadcount)) {
      ConnectivityService.networkClose(getToBeReadBook(true), context);

      // getToBeReadBook(true);
    }
  }

  Future<void> getUserDetails() async {
    try {
      isLoading = true;
      await Provider.of<UserController>(context, listen: false)
          .getUserDetailsByUserId(widget.userId, context)
          .then((_) {
        if (mounted) {
          userModel =
              Provider.of<UserController>(context, listen: false).userModel;
        }
      });

      isLoading = false;
    } catch (e) {
      log('An error occurred: $e');
    }
  }

  Future<void> getCurrentReadBookCase(bool isMore) async {
    if ((currentlyReadingBooks.length) <= currentReadcount || !isMore) {
      currentReadLoading = true;

      if (isMore) {
        currentReadLimit += 10;
      }
    }
    try {
      await Provider.of<BookCaseController>(context, listen: false)
          .getCurrentReadBookCase(
              widget.userId, currentReadLimit, offset, context)
          .then((responseMap) async {
        if (responseMap["statusCode"] == 200) {
          List<BookCaseModel> bookList = [];
          currentReadcount = responseMap['count'];
          bookList = (responseMap["data"] as List)
              .map((item) => BookCaseModel.fromJson(item))
              .toList();

          var result = DataHelper.getCurrentlyReadingAndTopShelfBooks(bookList);
          if (result.isNotEmpty) {
            // isLoading = false;
            currentlyReadingBooks = result[0];
          }

          if (result.length >= currentReadcount) {
            currentReadLoading = false;
          }
          // isLoading = false;
        }
      }).whenComplete(() {
        currentReadLoading = false;
      });
    } catch (e) {
      log(e.toString());
    }
  }

  Future<void> getToBeReadBook(bool isMore) async {
    if ((toBeReadList?.length ?? 0) <= toBeReadcount || !isMore) {
      toBeReadLoading = true;

      if (isMore) {
        toBeReadLimit += 10; // Increment the limit by 10 for the next load
      }
    }
    log("Current Read Limit : $toBeReadLimit");
    try {
      await Provider.of<BookCaseController>(context, listen: false)
          .getToBeReadBook(widget.userId, toBeReadLimit, offset, context)
          .then((responseMap) async {
        log("Response Map : ${responseMap['statusCode']}");
        if (responseMap["statusCode"] == 200) {
          List<BookCaseModel> bookCaseList = [];
          if (responseMap["count"] != null) {
            toBeReadcount = responseMap['count'];
          } else {
            toBeReadcount = 0;
          }
          log("ToBeRead Count : $toBeReadcount");

          if (responseMap["data"] != null) {
            bookCaseList = (responseMap["data"] as List)
                .map((item) => BookCaseModel.fromJson(item))
                .toList();
            toBeReadList = bookCaseList;
            // setState(() {});
            // bookCaseController?.notifyListeners();
          } else {
            toBeReadList?.clear();
            toBeReadList = [];
          }
          log("BookCase List : ${bookCaseList.length}");
        } else if (responseMap['statusCode'] == 404) {
          log("ToBeRead Read : ${toBeReadList?.length}");

          toBeReadcount = 0;
        }
      }).whenComplete(() {
        toBeReadLoading = false;
      });
      log("ToBeRead Read : ${toBeReadList?.length}");
    } catch (e) {
      log(e.toString());
    }
  }

  Future<void> getTopShelfBookCase(bool isMore) async {
    if ((topShelfBooks.length) <= topShelfCount || !isMore) {
      topShelfLoading = true;

      if (isMore) {
        topShelfLimit += 10;
      }
    }

    try {
      await Provider.of<BookCaseController>(context, listen: false)
          .allBooksRead(
              widget.userId, topShelfLimit, offset, false, false, context)
          .then((responseMap) async {
        if (responseMap["statusCode"] == 200) {
          List<BookCaseModel> bookList = [];
          topShelfCount = responseMap['count'];
          bookList = (responseMap["data"] as List)
              .map((item) => BookCaseModel.fromJson(item))
              .toList();
          // Provider.of<BookCaseController>(context, listen: false)
          //     .notifyListeners();
          var result = DataHelper.getCurrentlyReadingAndTopShelfBooks(bookList);
          if (result.isNotEmpty) {
            // isLoading = false;
            topShelfBooks = result[1];
            completedBooks = result[2];
            log("Completed Book Length: ${completedBooks.length}");
          }

          // print("Length : ${topShelfBooks?.length}");
          if ((topShelfBooks.length) >= topShelfCount) {
            topShelfLoading = false;
          }
          // isLoading = false;
        } else {}
      }).whenComplete(() {
        topShelfLoading = false;
      });
    } catch (e) {
      log(e.toString());
    }
  }

  int offSet = 0;
  int standingLimit = 10;
  int impromptuLimit = 10;
  bool standingLoading = false;
  bool impromptuLoading = false;
  int standingClubCount = 0;
  int impromptuClubCount = 0;

  Future<void> getStandingBookClubsByUserId(bool isMore) async {
    if ((standingBookClubs?.length ?? 0) <= standingClubCount || !isMore) {
      // isLoading = true;
      standingLoading = true;
      bookClubController?.notifyListeners();

      if (isMore) {
        standingLimit += 10;
      }
    }
    await Provider.of<BookClubController>(context, listen: false)
        .getBookClubs(
      ClubType.standing.value,
      widget.userId,
      null,
      context,
      offSet,
      standingLimit,
    )
        .then((responseMap) async {
      if (responseMap["statusCode"] == 200) {
        List<BookClubModel> bookClubList = [];

        if (responseMap["count"] != 0) {
          standingClubCount = responseMap['count'];
        } else {
          standingClubCount = 0;
        }
        if (responseMap["data"].isNotEmpty) {
          bookClubList = (responseMap["data"] as List)
              .map((item) => BookClubModel.fromJson(item))
              .toList();
          standingBookClubs = bookClubList;
          // notifyListeners();
        } else {
          standingBookClubs?.clear();
          standingBookClubs = [];
        }
        log("Standing Book Club Count : $standingClubCount");
      } else {
        standingBookClubs = [];
      }
    }).whenComplete(() {
      standingLoading = false;
      bookClubController?.notifyListeners();
    });
  }

  Future<void> getImpromptuBookClubsByUserId(bool isMore) async {
    if ((impromptuBookClubs?.length ?? 0) <= impromptuClubCount || !isMore) {
      impromptuLoading = true;
      bookClubController?.notifyListeners();

      if (isMore) {
        impromptuLimit += 10;
      }
    }
    log("Impromptu limit : $impromptuLimit");

    await Provider.of<BookClubController>(context, listen: false)
        .getBookClubs(
      ClubType.impromptu.value,
      widget.userId,
      null,
      context,
      offSet,
      impromptuLimit,
    )
        .then((responseMap) async {
      if (responseMap["statusCode"] == 200) {
        List<BookClubModel> bookClubList = [];

        if (responseMap["count"] != 0) {
          impromptuClubCount = responseMap['count'];
        } else {
          impromptuClubCount = 0;
        }
        if (responseMap["data"].isNotEmpty) {
          bookClubList = (responseMap["data"] as List)
              .map((item) => BookClubModel.fromJson(item))
              .toList();
          impromptuBookClubs = bookClubList;
        } else {
          impromptuBookClubs?.clear();
          impromptuBookClubs = [];
        }

        log("Impromptu Count : $impromptuClubCount");
        log("Impromptu List : ${impromptuBookClubs?.length}");
      } else {
        impromptuBookClubs = [];
      }
    }).whenComplete(() {
      impromptuLoading = false;
      bookClubController?.notifyListeners();
    });
  }

  int limit = 1000;

  Future<void> getBookClubsByUserId() async {
    await Provider.of<BookClubController>(context, listen: false)
        .getBookClubs(
      '',
      widget.userId,
      null,
      context,
      offset,
      limit,
    )
        .then((responseMap) async {
      if (responseMap["statusCode"] == 200) {
        List<BookClubModel> bookClubList = [];
        // setState(() {
        bookClubList = (responseMap["data"] as List)
            .map((item) => BookClubModel.fromJson(item))
            .toList();
        // });
        var result = DataHelper.getStandingAndImprmptuClubs(bookClubList);
        // setState(() {
        standingBookClubs = result[0];
        impromptuBookClubs = result[1];
        // });
      } else {}
    });
  }

  Future<void> _initializeUserId() async {
    loggedInUserId = _sessionManager.userId;
  }

  Future<void> getClubsLedByUser() async {
    await Provider.of<BookClubController>(context, listen: false)
        .getBookClubs(
      '',
      loggedInUserId,
      null,
      context,
      offset,
      limit,
    )
        .then((responseMap) async {
      if (responseMap["statusCode"] == 200) {
        List<BookClubModel> bookClubList = [];
        // setState(() {
        bookClubList = (responseMap["data"] as List)
            .map((item) => BookClubModel.fromJson(item))
            .toList();
        // });
        var result =
            DataHelper.getClubsLedByUser(bookClubList, loggedInUserId ?? 0);
        //clubsLedByUser = result;
        if (result.isEmpty) {
          showNoClubsLedValidation(
              "To send an invite you need to be a leader of a club. Start your own club to become a leader.");
        } else {
          clubsLedByUser =
              result.where((club) => (club.totalVacancies ?? 0) > 0).toList();

          if (clubsLedByUser.isEmpty) {
            showNoClubsLedValidation(
                "All your groups are currently full. You can invite members when new spots open up.");
          } else {
            showInvitePopup();
          }
        }
      } else {}
    });
  }

  void navigateToBookClubDetails(
      int? bookClubId, String? bookClubName, String? impromptuCount) {
    context.pushNamed(
      'club-details',
      extra: {
        'bookClubId': bookClubId,
        'bookClubName': bookClubName,
        'impromptuCount': impromptuCount,
      },
    );
  }

  void clearValidationMessage() {
    setState(() {
      bookClubValidation = false;
      invitationMsgValidation = false;
    });
  }

  @override
  void dispose() {
    _currentReadScrollController.dispose();
    _topShelfScrollController.dispose();
    _standingClubscrollController.dispose();
    _impromClubScrollController.dispose();
    _toBeReadScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (isTrue) {
      return Scaffold(
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(80),
          child: Container(
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  width: 1.5,
                  color: AppConstants.primaryColor,
                ),
              ),
            ),
            child: Skeletonizer(
              effect: const SoldColorEffect(
                color: AppConstants.skeletonforgroundColor,
                lowerBound: 0.1,
                upperBound: 0.5,
              ),
              containersColor: AppConstants.textGreenColor,
              enabled: isLoading,
              child: Consumer<UserController>(
                builder: (context, userController, child) {
                  log('dp: ${userModel?.data?.userProfilePicture}');
                  return AppBar(
                    backgroundColor: AppConstants.textGreenColor,
                    leading: Padding(
                      padding: const EdgeInsets.only(left: 20.0, top: 10),
                      child: NetworkAwareTap(
                        onTap: () {
                          widget.fromChat ?? false
                              ? context.goNamed(
                                  'chat-screen',
                                  queryParameters: {
                                    'userId': widget.userId.toString(),
                                    'bookClubId': widget.bookClubId.toString(),
                                  },
                                )
                              : GoRouter.of(context).pop();
                          // context.pop();
                        },
                        child: SvgPicture.asset(
                          "assets/icons/svg/Back.svg",
                          width: 73,
                          height: 65,
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                    centerTitle: true,
                    title: Padding(
                      padding: const EdgeInsets.only(top: 10.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(50),
                                child: CustomCachedNetworkImage(
                                  imageUrl: image +
                                      (userModel?.data?.userProfilePicture ??
                                          ''),
                                  width: 45,
                                  height: 45,
                                  errorImage: AppConstants.profileLogoImagePath,
                                ),
                              ),
                              const SizedBox(
                                width: 8,
                              ),
                              ConstrainedBox(
                                constraints: BoxConstraints(
                                  maxWidth:
                                      MediaQuery.of(context).size.width * 0.5,
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    MarqueeList(
                                      children: [
                                        Text(
                                          userModel?.data?.userName ?? '',
                                          style: lbBold.copyWith(
                                            fontSize: 18,
                                          ),
                                        ),
                                      ],
                                    ),
                                    Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Flexible(
                                          child: Text(
                                            userModel?.data?.userHandle ?? '',
                                            style: lbBold.copyWith(
                                              fontSize: 14,
                                            ),
                                          ),
                                        ),
                                        const SizedBox(
                                          width: 8,
                                        ),
                                        if (isDelay) ...[
                                          const SizedBox.shrink(),
                                        ] else ...[
                                          if (userModel
                                                  ?.data?.userClubInvitation ??
                                              false) ...[
                                            Image.asset(
                                              AppConstants
                                                  .openToInvitationImagePath,
                                              height: 15,
                                              width: 15,
                                            ),
                                          ] else if (!(userModel
                                                  ?.data?.userClubInvitation ??
                                              false)) ...[
                                            Image.asset(
                                              AppConstants
                                                  .notOpenToInvitationImagePath,
                                              height: 20,
                                              width: 20,
                                            ),
                                          ],
                                        ],
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    actions: [
                      Padding(
                        padding: const EdgeInsets.only(right: 20.0, top: 10),
                        child: NetworkAwareTap(
                          onTap: () {
                            showDialog(
                              context: context,
                              barrierColor: Colors.white60,
                              builder: (BuildContext context) {
                                return const QuestionFeedbackDialog();
                              },
                            );
                          },
                          child: Skeleton.replace(
                            replacement: ClipRRect(
                              borderRadius: BorderRadius.circular(20),
                              child: Image.asset(
                                AppConstants.questionLogoImagePath,
                                height: 34,
                                width: 34,
                              ),
                            ),
                            child: Image.asset(
                              AppConstants.questionLogoImagePath,
                              height: 34,
                              width: 34,
                            ),
                          ),
                        ),
                      )
                    ],
                  );
                  // return ProfileAppBar(
                  //   userName: userModel?.data?.userName,
                  //   userHandle: userModel?.data?.userHandle,
                  //   isOpenToClubInvitation:
                  //       userModel?.data?.userClubInvitation ?? false,
                  //   userOwnProfile: false,
                  //   userProfilePicture: userModel?.data?.userProfilePicture,
                  // );
                },
              ),
            ),
          ),
        ),
        body: Container(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                AppConstants.bgImagePath,
              ),
              filterQuality: FilterQuality.high,
              fit: BoxFit.fitWidth,
            ),
          ),
          child: Skeletonizer(
            effect: const SoldColorEffect(
              color: AppConstants.skeletonforgroundColor,
              lowerBound: 0.1,
              upperBound: 0.5,
            ),
            containersColor: AppConstants.skeletonBackgroundColor,
            enabled: isLoading,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: Row(
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(top: 25.0),
                          child: Image.asset(
                            AppConstants.locationImagePath,
                            height: 25,
                            width: 25,
                            filterQuality: FilterQuality.high,
                            fit: BoxFit.cover,
                          ),
                        ),
                        const SizedBox(
                          width: 5,
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 35.0),
                          child: ConstrainedBox(
                            constraints: BoxConstraints(
                                maxWidth:
                                    MediaQuery.of(context).size.width / 2),
                            child: Text(
                              userModel?.data?.userLocation ?? '',
                              overflow: TextOverflow.ellipsis,
                              style: lbRegular.copyWith(
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ),
                        const Spacer(),
                        Padding(
                          padding: const EdgeInsets.only(top: 35.0),
                          child: Text(
                            "Joined: $formattedDate",
                            style: lbRegular.copyWith(
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 15,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: Text(
                      userModel?.data?.userBio ?? '',
                      style: lbRegular.copyWith(
                        fontSize: 14,
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 15,
                  ),
                  userModel?.data?.userClubInvitation ?? false
                      ? Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              NetworkAwareTap(
                                onTap: () {
                                  FellowReaderMatchesBottomSheet();
                                  getClubsLedByUser();
                                },
                                child: Skeleton.replace(
                                  replacement: inviteButtonSkeleton(true),
                                  child: inviteButtonSkeleton(false),
                                ),
                              ),
                              NetworkAwareTap(
                                onTap: () {
                                  if ((userModel?.data?.matches ?? 0) > 0) {
                                    fellowReaderMatchesBottomSheet(
                                      userModel?.data?.currentlyReadingBooks ??
                                          [],
                                      userModel?.data?.toBeReadBooks ?? [],
                                      userModel?.data?.fiveStarMatchBooks ?? [],
                                      userModel?.data?.userName ?? '',
                                      userModel?.data?.userId ?? 0,
                                    );
                                  } else {
                                    log("No Matches Found");
                                  }
                                },
                                child: Skeleton.replace(
                                  replacement: matchButtonSkeleton(true),
                                  child: matchButtonSkeleton(false),
                                ),
                              ),
                            ],
                          ),
                        )
                      : const SizedBox.shrink(),

                  userModel?.data?.userClubInvitation ?? false
                      ? const SizedBox(
                          height: 25,
                        )
                      : const SizedBox.shrink(),
                  const Divider(
                    thickness: 1.5,
                    color: AppConstants.primaryColor,
                  ),
                  const SizedBox(
                    height: 18,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: Row(
                      children: [
                        Text(
                          "Bookcase",
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 24,
                          ),
                        ),
                        const SizedBox(
                          width: 10,
                        ),
                        Image.asset(
                          "assets/icons/Bookcase_2.png",
                          height: 25,
                          width: 25,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 18,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: Row(
                      children: [
                        Text(
                          "Currently Reading",
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 20,
                          ),
                        ),
                        const SizedBox(
                          width: 10,
                        ),
                        Image.asset(
                          AppConstants.currentlyReadingIcon,
                          height: 20,
                          width: 22,
                          filterQuality: FilterQuality.high,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  if (currentlyReadingBooks.isEmpty) ...[
                    Skeleton.replace(
                      replacement: Container(
                        padding: const EdgeInsets.only(left: 20),
                        margin: const EdgeInsets.symmetric(horizontal: 20),
                        height: 50,
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                          color: AppConstants.skeletonBackgroundColor,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            "No books in currently reading",
                            textAlign: TextAlign.start,
                          ),
                        ),
                      ),
                      child: const NoDataWidget(
                        message: "No books in currently reading",
                      ),
                    ),
                  ] else ...[
                    SizedBox(
                      height: 77,
                      child: Consumer<BookCaseController>(
                          builder: (context, bookCaseController, child) {
                        return ListView.builder(
                          controller: _currentReadScrollController,
                          scrollDirection: Axis.horizontal,
                          padding: const EdgeInsets.only(left: 10, right: 20),
                          itemCount: currentReadLoading
                              ? (currentlyReadingBooks.length) + 1
                              : currentlyReadingBooks.length,
                          itemBuilder: (context, index) {
                            if (index == currentlyReadingBooks.length &&
                                currentReadLoading) {
                              return const Padding(
                                padding: EdgeInsets.only(left: 10.0),
                                child: Center(
                                  child: CircularProgressIndicator(
                                    color: AppConstants.primaryColor,
                                  ),
                                ),
                              );
                            }
                            return NetworkAwareTap(
                              onTap: () {},
                              child: Skeleton.replace(
                                replacement: currentReadSkeleton(true, index),
                                child: currentReadSkeleton(false, index),
                              ),
                            );
                          },
                        );
                      }),
                    ),
                  ],

                  const SizedBox(
                    height: 25,
                  ),
                  // Padding(
                  //   padding: const EdgeInsets.symmetric(horizontal: 20.0),
                  //   child: Row(
                  //     children: [
                  //       Text(
                  //         "Topshelf",
                  //         textAlign: TextAlign.center,
                  //         style: lbRegular.copyWith(
                  //           fontSize: 20,
                  //         ),
                  //       ),
                  //       const SizedBox(
                  //         width: 10,
                  //       ),
                  //       Image.asset(
                  //         AppConstants.topShelfBookIcon,
                  //         height: 20,
                  //         width: 22,
                  //         filterQuality: FilterQuality.high,
                  //       ),
                  //     ],
                  //   ),
                  // ),
                  // const SizedBox(
                  //   height: 10,
                  // ),
                  // if (topShelfBooks.isEmpty) ...[
                  //   Skeleton.replace(
                  //     replacement: Container(
                  //       padding: const EdgeInsets.only(left: 20),
                  //       margin:
                  //           const EdgeInsets.symmetric(horizontal: 20),
                  //       height: 50,
                  //       width: MediaQuery.of(context).size.width,
                  //       decoration: BoxDecoration(
                  //         color: AppConstants.skeletonBackgroundColor,
                  //         borderRadius: BorderRadius.circular(10),
                  //       ),
                  //       child: const Align(
                  //         alignment: Alignment.centerLeft,
                  //         child: Text(
                  //           "No books in topshelf",
                  //           textAlign: TextAlign.start,
                  //         ),
                  //       ),
                  //     ),
                  //     child: const NoDataWidget(
                  //       message: "No books in topshelf",
                  //     ),
                  //   ),
                  // ] else ...[
                  //   SizedBox(
                  //     height: 120,
                  //     child: Consumer<BookCaseController>(
                  //         builder: (context, bookCaseController, child) {
                  //       return ListView.builder(
                  //         controller: _topShelfScrollController,
                  //         scrollDirection: Axis.horizontal,
                  //         padding:
                  //             const EdgeInsets.only(left: 10, right: 20),
                  //         itemCount: topShelfLoading
                  //             ? (topShelfBooks.length) + 1
                  //             : topShelfBooks.length,
                  //         itemBuilder: (context, index) {
                  //           if (index == topShelfBooks.length &&
                  //               topShelfLoading) {
                  //             return const Padding(
                  //               padding: EdgeInsets.only(left: 10.0),
                  //               child: Center(
                  //                 child: CircularProgressIndicator(
                  //                   color: AppConstants.primaryColor,
                  //                 ),
                  //               ),
                  //             );
                  //           }
                  //           return Skeleton.replace(
                  //             replacement: topShelfSkeleton(true, index),
                  //             child: topShelfSkeleton(false, index),
                  //           );
                  //         },
                  //       );
                  //     }),
                  //   ),
                  // ],

                  /// TO-BE-READ 8 JAN 2025

                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: Row(
                      children: [
                        Text(
                          "To-Be-Read",
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 20,
                          ),
                        ),
                        const SizedBox(
                          width: 10,
                        ),
                        Image.asset(
                          AppConstants.toBeReadIcon,
                          height: 20,
                          width: 26,
                          filterQuality: FilterQuality.high,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  toBeReadList?.isNotEmpty ?? false
                      ? SizedBox(
                          height: 80,
                          child: Consumer<BookCaseController>(
                            builder: (context, bookCaseController, child) {
                              return ListView.builder(
                                controller: _toBeReadScrollController,
                                scrollDirection: Axis.horizontal,
                                padding:
                                    const EdgeInsets.only(left: 10, right: 20),
                                itemCount: toBeReadLoading
                                    ? (toBeReadList?.length ?? 0) + 1
                                    : toBeReadList?.length,
                                itemBuilder: (context, index) {
                                  if (index == toBeReadList?.length &&
                                      toBeReadLoading) {
                                    return const Padding(
                                      padding: EdgeInsets.only(left: 10.0),
                                      child: Center(
                                        child: CircularProgressIndicator(
                                          color: AppConstants.primaryColor,
                                        ),
                                      ),
                                    );
                                  }
                                  return Skeleton.replace(
                                    replacement: toBeReadSkeleton(true, index),
                                    child: toBeReadSkeleton(false, index),
                                  );
                                },
                              );
                            },
                          ),
                        )
                      : Skeleton.replace(
                          replacement: Container(
                            padding: const EdgeInsets.only(left: 20),
                            margin: const EdgeInsets.symmetric(horizontal: 20),
                            height: 50,
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                              color: AppConstants.skeletonBackgroundColor,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: const Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                "No books in To-Be-Read",
                                textAlign: TextAlign.start,
                              ),
                            ),
                          ),
                          child: const NoDataWidget(
                            message: "No books in To-Be-Read",
                          ),
                        ),

                  const SizedBox(
                    height: 25,
                  ),

                  /// VIEW ALL BOOKS READ

                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: Row(
                      children: [
                        Text(
                          "All Books Read",
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 20,
                          ),
                        ),
                        const SizedBox(
                          width: 10,
                        ),
                        Image.asset(
                          AppConstants.bookReadIcon,
                          height: 20,
                          width: 22,
                          filterQuality: FilterQuality.high,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),

                  completedBooks.isNotEmpty
                      ? SizedBox(
                          height: 117,
                          child: Consumer<BookCaseController>(
                            builder: (context, bookCaseController, child) {
                              return ListView.builder(
                                controller: _topShelfScrollController,
                                scrollDirection: Axis.horizontal,
                                padding:
                                    const EdgeInsets.only(left: 10, right: 20),
                                itemCount: topShelfLoading
                                    ? (completedBooks.length) + 1
                                    : completedBooks.length,
                                itemBuilder: (context, index) {
                                  if (index == completedBooks.length &&
                                      topShelfLoading) {
                                    return const Padding(
                                      padding: EdgeInsets.only(left: 10.0),
                                      child: Center(
                                        child: CircularProgressIndicator(
                                          color: AppConstants.primaryColor,
                                        ),
                                      ),
                                    );
                                  }
                                  return Skeleton.replace(
                                    replacement:
                                        completeReadSkeleton(true, index),
                                    child: completeReadSkeleton(false, index),
                                  );
                                },
                              );
                            },
                          ),
                        )
                      : Skeleton.replace(
                          replacement: Container(
                            padding: const EdgeInsets.only(left: 20),
                            margin: const EdgeInsets.symmetric(horizontal: 20),
                            height: 50,
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                              color: AppConstants.skeletonBackgroundColor,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: const Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                "No books read",
                                textAlign: TextAlign.start,
                              ),
                            ),
                          ),
                          child: const NoDataWidget(
                            message: "No books read",
                          ),
                        ),
                  const SizedBox(
                    height: 25,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: NetworkAwareTap(
                      onTap: () {
                        context.pushNamed(
                          'club-member-bookcase',
                          extra: {
                            'userName': widget.userName,
                            'userHandle': userModel?.data?.userHandle,
                            'userClubInvitation':
                                userModel?.data?.userClubInvitation,
                            'userId': widget.userId,
                            'userProfilePicture':
                                userModel?.data?.userProfilePicture ?? '',
                          },
                        );
                      },
                      child: Container(
                        height: 45,
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(49),
                          color: AppConstants.textGreenColor,
                        ),
                        child: Center(
                          child: Text(
                            "View All Books Read",
                            textAlign: TextAlign.center,
                            style: lbBold.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 25,
                  ),
                  const Divider(
                    thickness: 1.5,
                    color: AppConstants.primaryColor,
                  ),
                  const SizedBox(
                    height: 18,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: Row(
                      children: [
                        Text(
                          "Book Clubs",
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 24,
                          ),
                        ),
                        const SizedBox(
                          width: 10,
                        ),
                        Image.asset(
                          "assets/icons/Clubs_2.png",
                          height: 35,
                          width: 35,
                          filterQuality: FilterQuality.high,
                          fit: BoxFit.contain,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 18,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: Text(
                      "Standing Book Clubs",
                      textAlign: TextAlign.center,
                      style: lbRegular.copyWith(
                        fontSize: 20,
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  if (standingBookClubs?.isEmpty ?? false) ...[
                    Skeleton.replace(
                      replacement: Container(
                        padding: const EdgeInsets.only(left: 20),
                        margin: const EdgeInsets.symmetric(horizontal: 20),
                        height: 50,
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                          color: AppConstants.skeletonBackgroundColor,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            "This user is yet to join a bookclub",
                            textAlign: TextAlign.start,
                          ),
                        ),
                      ),
                      child: const NoDataWidget(
                        message: "This user is yet to join a bookclub",
                      ),
                    ),
                  ] else ...[
                    SizedBox(
                      height: 132,
                      child: Consumer<BookClubController>(
                        builder: (context, bookClubController, child) {
                          return ListView.builder(
                            controller: _standingClubscrollController,
                            scrollDirection: Axis.horizontal,
                            padding: const EdgeInsets.only(left: 10, right: 20),
                            itemCount: (standingLoading)
                                ? (standingBookClubs?.length ?? 0) + 1
                                : standingBookClubs?.length,
                            itemBuilder: (context, index) {
                              if (index == standingBookClubs?.length &&
                                  (standingLoading)) {
                                return const Padding(
                                  padding: EdgeInsets.only(left: 10.0),
                                  child: Center(
                                    child: CircularProgressIndicator(
                                      color: AppConstants.primaryColor,
                                    ),
                                  ),
                                );
                              }
                              return NetworkAwareTap(
                                onTap: () {
                                  Provider.of<BookClubController>(context,
                                          listen: false)
                                      .updateData(
                                    standingBookClubs?[index] ??
                                        BookClubModel(),
                                  );
                                  if (standingBookClubs?[index].userId ==
                                      loggedInUserId) {
                                    context.pushNamed(
                                      "user-club-details",
                                      queryParameters: {
                                        'bookClubId': standingBookClubs?[index]
                                            .bookClubId
                                            .toString(),
                                        'userId': loggedInUserId.toString(),
                                      },
                                    );
                                  } else {
                                    bool isMemeberExist =
                                        standingBookClubs?[index]
                                                .memberUserIds
                                                ?.contains(loggedInUserId) ??
                                            false;
                                    isMemeberExist
                                        ? context.pushNamed(
                                            "user-club-details",
                                            queryParameters: {
                                              'bookClubId':
                                                  standingBookClubs?[index]
                                                      .bookClubId
                                                      .toString(),
                                              'userId':
                                                  loggedInUserId.toString(),
                                            },
                                          ).then((e) async {
                                            if (context.mounted) {
                                              final isLeaveClub =
                                                  bookClubController
                                                      .isLeaveClub;
                                              if (isLeaveClub) {
                                                getStandingBookClubsByUserId(
                                                    true);
                                              }
                                            }
                                          })
                                        : navigateToBookClubDetails(
                                            standingBookClubs?[index]
                                                .bookClubId,
                                            standingBookClubs?[index]
                                                .bookClubName,
                                            '',
                                          );
                                  }
                                },
                                child: Skeleton.replace(
                                  replacement:
                                      standingClubSkeleton(true, index),
                                  child: standingClubSkeleton(false, index),
                                ),
                              );
                            },
                          );
                        },
                      ),
                    ),
                  ],
                  const SizedBox(
                    height: 25,
                  ),

                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: Text(
                      "Impromptu Book Clubs",
                      textAlign: TextAlign.center,
                      style: lbRegular.copyWith(
                        fontSize: 20,
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  if (impromptuBookClubs?.isEmpty ?? false) ...[
                    Skeleton.replace(
                      replacement: Container(
                        padding: const EdgeInsets.only(left: 20),
                        margin: const EdgeInsets.symmetric(horizontal: 20),
                        height: 50,
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                          color: AppConstants.skeletonBackgroundColor,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            "This user is yet to join a bookclub",
                            textAlign: TextAlign.start,
                          ),
                        ),
                      ),
                      child: const NoDataWidget(
                        message: "This user is yet to join a bookclub",
                      ),
                    ),
                  ] else ...[
                    SizedBox(
                      height: 181,
                      child: Consumer<BookClubController>(
                          builder: (context, bookClubController, child) {
                        return ListView.builder(
                          controller: _impromClubScrollController,
                          scrollDirection: Axis.horizontal,
                          padding: const EdgeInsets.only(left: 10, right: 20),
                          itemCount: (impromptuLoading)
                              ? (impromptuBookClubs?.length ?? 0) + 1
                              : impromptuBookClubs?.length,
                          itemBuilder: (context, index) {
                            if (index == impromptuBookClubs?.length &&
                                (impromptuLoading)) {
                              return const Padding(
                                padding: EdgeInsets.only(left: 10.0),
                                child: Center(
                                  child: CircularProgressIndicator(
                                    color: AppConstants.primaryColor,
                                  ),
                                ),
                              );
                            }
                            return NetworkAwareTap(
                              onTap: () {
                                Provider.of<BookClubController>(context,
                                        listen: false)
                                    .updateData(
                                  impromptuBookClubs?[index] ?? BookClubModel(),
                                );
                                if (impromptuBookClubs?[index].userId ==
                                    loggedInUserId) {
                                  context.pushNamed(
                                    "user-club-details",
                                    queryParameters: {
                                      'bookClubId': impromptuBookClubs?[index]
                                          .bookClubId
                                          .toString(),
                                      'userId': loggedInUserId.toString(),
                                    },
                                  );
                                } else {
                                  bool isMemeberExist =
                                      impromptuBookClubs?[index]
                                              .memberUserIds
                                              ?.contains(loggedInUserId) ??
                                          false;
                                  isMemeberExist
                                      ? context.pushNamed(
                                          "user-club-details",
                                          queryParameters: {
                                            'bookClubId':
                                                impromptuBookClubs?[index]
                                                    .bookClubId
                                                    .toString(),
                                            'userId': loggedInUserId.toString(),
                                          },
                                        ).then((e) async {
                                          if (context.mounted) {
                                            final isLeaveClub =
                                                bookClubController.isLeaveClub;
                                            if (isLeaveClub) {
                                              getImpromptuBookClubsByUserId(
                                                  true);
                                            }
                                          }
                                        })
                                      : navigateToBookClubDetails(
                                          impromptuBookClubs?[index].bookClubId,
                                          impromptuBookClubs?[index]
                                              .bookClubName,
                                          impromptuBookClubs?[index].clubCount,
                                        );
                                }
                              },
                              child: Skeleton.replace(
                                replacement: impromptuClubSkeleton(true, index),
                                child: impromptuClubSkeleton(false, index),
                              ),
                            );
                          },
                        );
                      }),
                    ),
                  ],
                  const SizedBox(
                    height: 25,
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    } else {
      return Scaffold(
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(80),
          child: Container(
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  width: 1.5,
                  color: Color.fromRGBO(37, 57, 67, 1),
                ),
              ),
            ),
            child: ProfileAppBar(
              userName: widget.userName,
              userHandle: userModel?.data?.userHandle,
              isOpenToClubInvitation:
                  userModel?.data?.userClubInvitation ?? false,
              userOwnProfile: false,
            ),
          ),
        ),
        body: Container(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                AppConstants.bgImagePath,
              ),
              filterQuality: FilterQuality.high,
              fit: BoxFit.fitWidth,
            ),
          ),
          child: Column(
            children: [
              const SizedBox(
                height: 25,
              ),
              Text(
                "Account Blocked",
                textAlign: TextAlign.center,
                style: lbRegular.copyWith(
                  fontSize: 20,
                ),
              ),
              const SizedBox(
                height: 25,
              ),
              Text(
                "Manage blocked accounts in ‘Settings’",
                textAlign: TextAlign.center,
                style: lbRegular.copyWith(
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget toBeReadSkeleton(bool isBorder, int index) {
    return Container(
      margin: const EdgeInsets.only(left: 10),
      width: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MarqueeList(
              children: [
                Text(
                  toBeReadList?[index].bookName ?? '',
                  textAlign: TextAlign.start,
                  overflow: TextOverflow.ellipsis,
                  style: lbBold.copyWith(
                    fontSize: 18,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 3,
            ),
            MarqueeList(
              children: [
                Text(
                  toBeReadList?[index].bookAuthor ?? '',
                  textAlign: TextAlign.start,
                  overflow: TextOverflow.ellipsis,
                  style: lbRegular.copyWith(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget completeReadSkeleton(bool isBorder, int index) {
    return Container(
      margin: const EdgeInsets.only(left: 10),
      width: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MarqueeList(
              children: [
                Text(
                  completedBooks[index].bookName ?? '',
                  textAlign: TextAlign.start,
                  overflow: TextOverflow.ellipsis,
                  style: lbBold.copyWith(
                    fontSize: 18,
                  ),
                ),
              ],
            ),
            MarqueeList(
              children: [
                Text(
                  completedBooks[index].bookAuthor ?? '',
                  textAlign: TextAlign.start,
                  overflow: TextOverflow.ellipsis,
                  style: lbRegular.copyWith(),
                ),
              ],
            ),
            const SizedBox(
              height: 20,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                (completedBooks[index].review?.isNotEmpty ?? false)
                    ? NetworkAwareTap(
                        onTap: () {
                          context.pushNamed(
                            "book-review",
                            extra: {
                              // 'index': index,
                              'userName': userModel?.data?.userName,
                              'bookName': completedBooks[index].bookName,
                              'bookAuthor': completedBooks[index].bookAuthor,
                              'userHandle': userModel?.data?.userHandle,
                              'ratings': completedBooks[index].ratings,
                              'review': completedBooks[index].review,
                              'userProfile':
                                  userModel?.data?.userProfilePicture,
                              'userClubInvitation':
                                  userModel?.data?.userClubInvitation ?? false,
                              'userOwnProfile': true,
                            },
                          );
                        },
                        child: Text(
                          "Review",
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                          style: lbItalic.copyWith(
                            fontSize: 12,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      )
                    : RatingBar(
                        ignoreGestures: true,
                        itemCount: 5,
                        itemSize: 20,
                        allowHalfRating: true,
                        initialRating: completedBooks[index].ratings ?? 0,
                        minRating: 0,
                        ratingWidget: RatingWidget(
                          full: const Icon(
                            Icons.star,
                            color: AppConstants.textGreenColor,
                          ),
                          half: const Icon(
                            Icons.star_half,
                            color: AppConstants.textGreenColor,
                          ),
                          empty: const Icon(
                            Icons.star_border_outlined,
                            color: AppConstants.textGreenColor,
                          ),
                        ),
                        onRatingUpdate: (double value) {},
                      ),
                const SizedBox(
                  width: 10,
                ),
                (completedBooks[index].review?.isNotEmpty ?? false)
                    ? RatingBar(
                        ignoreGestures: true,
                        itemCount: 5,
                        itemSize: 20,
                        allowHalfRating: true,
                        initialRating: completedBooks[index].ratings ?? 0,
                        minRating: 0,
                        ratingWidget: RatingWidget(
                          full: const Icon(
                            Icons.star,
                            color: AppConstants.textGreenColor,
                          ),
                          half: const Icon(
                            Icons.star_half,
                            color: AppConstants.textGreenColor,
                          ),
                          empty: const Icon(
                            Icons.star_border_outlined,
                            color: AppConstants.textGreenColor,
                          ),
                        ),
                        onRatingUpdate: (double value) {},
                      )
                    : const SizedBox.shrink(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget standingClubSkeleton(bool isBorder, int index) {
    return Container(
      margin: const EdgeInsets.only(left: 10),
      width: 228,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        border: Border.all(
          color: isBorder
              ? AppConstants.skeletonBackgroundColor
              : AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Column(
          children: [
            Stack(
              alignment: Alignment.center,
              // mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Align(
                  alignment: Alignment.centerLeft,
                  child: Visibility(
                    replacement: const SizedBox.shrink(),
                    visible: standingBookClubs?[index].userId == widget.userId,
                    child: Image.asset(
                      AppConstants.leaderStar,
                      height: 43,
                      width: 43,
                      fit: BoxFit.cover,
                      filterQuality: FilterQuality.high,
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.center,
                  child: Image.asset(
                    (standingBookClubs?[index].totalVacancies ?? 0) > 0
                        ? AppConstants.clubOpeningLogoImagePath
                        : AppConstants.clubOpeningZero,
                    height: 50,
                    width: 50,
                    filterQuality: FilterQuality.high,
                    fit: BoxFit.cover,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 25,
            ),
            MarqueeList(
              children: [
                Text(
                  standingBookClubs?[index].bookClubName ?? '',
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: lbBold.copyWith(
                    fontSize: 18,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget impromptuClubSkeleton(bool isBorder, int index) {
    return Container(
      margin: const EdgeInsets.only(left: 10),
      width: 228,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        border: Border.all(
          color: isBorder
              ? AppConstants.skeletonBackgroundColor
              : AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Column(
          children: [
            Stack(
              alignment: Alignment.center,
              // mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Align(
                  alignment: Alignment.centerLeft,
                  child: Visibility(
                    replacement: const SizedBox.shrink(),
                    visible: impromptuBookClubs?[index].userId == widget.userId,
                    child: Image.asset(
                      AppConstants.leaderStar,
                      height: 43,
                      width: 43,
                      fit: BoxFit.cover,
                      filterQuality: FilterQuality.high,
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.center,
                  child: Image.asset(
                    (impromptuBookClubs?[index].totalVacancies ?? 0) > 0
                        ? AppConstants.clubOpeningLogoImagePath
                        : AppConstants.clubOpeningZero,
                    height: 50,
                    width: 50,
                    fit: BoxFit.cover,
                    filterQuality: FilterQuality.high,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 25,
            ),
            MarqueeList(
              children: [
                Text(
                  impromptuBookClubs?[index].bookClubName ?? '',
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: lbBold.copyWith(
                    fontSize: 18,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 3,
            ),
            Text(
              impromptuBookClubs?[index].bookAuthor ?? '',
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
              style: lbRegular.copyWith(
                fontSize: 14,
              ),
            ),
            const Spacer(),
            Text(
              impromptuBookClubs?[index].clubCount ?? '',
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
              style: lbItalic.copyWith(
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget inviteButtonSkeleton(bool isBorder) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),

      // height: 39,
      width: MediaQuery.of(context).size.width / 2.5,
      decoration: BoxDecoration(
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : AppConstants.textGreenColor,
        borderRadius: BorderRadius.circular(90),
        // border: Border.all(
        //   color: AppConstants.primaryColor,
        //   width: 1.5,
        // ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            "Invite",
            textAlign: TextAlign.center,
            style: lbBold.copyWith(
              fontSize: 14,
            ),
          ),
          const SizedBox(
            width: 6,
          ),
          Image.asset(
            AppConstants.openToInvitationImagePath,
            height: 18,
            width: 18,
          ),
        ],
      ),
    );
  }

  Widget matchButtonSkeleton(bool isBorder) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),

      // height: 39,
      width: MediaQuery.of(context).size.width / 2.5,
      decoration: BoxDecoration(
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : ((userModel?.data?.matches ?? 0) > 0)
                ? AppConstants.textGreenColor
                : AppConstants.isActiveRequestColor,
        borderRadius: BorderRadius.circular(90),
      ),

      /// ${userModel?.data?.matches ?? 0}
      child: Text(
        ((userModel?.data?.matches ?? 0) == 0)
            ? "No Match"
            : ((userModel?.data?.matches ?? 0) > 0 &&
                    (userModel?.data?.matches ?? 0) < 2)
                ? "${userModel?.data?.matches ?? 0} Match"
                : "${userModel?.data?.matches ?? 0} Matches",
        textAlign: TextAlign.center,
        style: lbBold.copyWith(
          fontSize: 14,
          color: ((userModel?.data?.matches ?? 0) > 0)
              ? AppConstants.primaryColor
              : Colors.black38,
        ),
      ),
    );
  }

  /// FELLOW READER MATCHES BOTTOM SHEET

  Future<void> fellowReaderMatchesBottomSheet(
    List<int> cIds,
    List<int> toBeReadIds,
    List<int> starIds,
    String userName,
    int userId,
  ) async {
    return showModalBottomSheet(
      barrierColor: Colors.white60,
      isScrollControlled: true,
      useRootNavigator: true,
      useSafeArea: true,
      context: context,
      constraints: BoxConstraints(
        minHeight: MediaQuery.of(context).size.height * 0.3,
      ),
      builder: (context) {
        return FellowReaderMatchesBottomSheet(
          currentlyReadMatch: cIds,
          fivestarMatch: starIds,
          tobeReadMatch: toBeReadIds,
          userName: userName,
          userId: userId,
        );
      },
    );
  }

  Widget currentReadSkeleton(bool isBorder, int index) {
    return Container(
      margin: const EdgeInsets.only(left: 10),
      width: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MarqueeList(
              children: [
                Text(
                  currentlyReadingBooks[index].bookName ?? '',
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.start,
                  style: lbBold.copyWith(
                    fontSize: 18,
                  ),
                ),
              ],
            ),
            MarqueeList(
              children: [
                Text(
                  currentlyReadingBooks[index].bookAuthor ?? '',
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.start,
                  style: lbRegular.copyWith(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget topShelfSkeleton(bool isBorder, int index) {
    return Container(
      margin: const EdgeInsets.only(left: 10),
      width: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MarqueeList(
              children: [
                Text(
                  topShelfBooks[index].bookName ?? '',
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.start,
                  style: lbBold.copyWith(
                    fontSize: 18,
                  ),
                ),
              ],
            ),
            MarqueeList(
              children: [
                Text(
                  topShelfBooks[index].bookAuthor ?? '',
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.start,
                  style: lbRegular.copyWith(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 20,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                (topShelfBooks[index].review != null &&
                        (topShelfBooks[index].review?.isNotEmpty ?? false))
                    ? NetworkAwareTap(
                        onTap: () {
                          context.pushNamed(
                            "book-review",
                            extra: {
                              // 'index': index,
                              'userName': userModel?.data?.userName,
                              'userHandle': userModel?.data?.userHandle,
                              'userClubInvitation':
                                  userModel?.data?.userClubInvitation,
                              'bookName': topShelfBooks[index].bookName,
                              'bookAuthor': topShelfBooks[index].bookAuthor,
                              'ratings': topShelfBooks[index].ratings,
                              'review': topShelfBooks[index].review,
                              'userProfile':
                                  userModel?.data?.userProfilePicture ?? '',
                            },
                          );
                        },
                        child: Text(
                          "Review",
                          textAlign: TextAlign.center,
                          style: lbItalic.copyWith(
                            fontSize: 12,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      )
                    : RatingBar(
                        ignoreGestures: true,
                        itemCount: 5,
                        itemSize: 20,
                        allowHalfRating: true,
                        initialRating: topShelfBooks[index].ratings ?? 0,
                        minRating: 0,
                        ratingWidget: RatingWidget(
                          full: const Icon(
                            Icons.star,
                            color: AppConstants.textGreenColor,
                          ),
                          half: const Icon(
                            Icons.star_half,
                            color: AppConstants.textGreenColor,
                          ),
                          empty: const Icon(
                            Icons.star_border_outlined,
                            color: AppConstants.textGreenColor,
                          ),
                        ),
                        onRatingUpdate: (double value) {},
                      ),
                const SizedBox(
                  width: 10,
                ),
                (topShelfBooks[index].review != null &&
                        (topShelfBooks[index].review?.isNotEmpty ?? false))
                    ? RatingBar(
                        ignoreGestures: true,
                        itemCount: 5,
                        itemSize: 20,
                        allowHalfRating: true,
                        initialRating: topShelfBooks[index].ratings ?? 0,
                        minRating: 0,
                        ratingWidget: RatingWidget(
                          full: const Icon(
                            Icons.star,
                            color: AppConstants.textGreenColor,
                          ),
                          half: const Icon(
                            Icons.star_half,
                            color: AppConstants.textGreenColor,
                          ),
                          empty: const Icon(
                            Icons.star_border_outlined,
                            color: AppConstants.textGreenColor,
                          ),
                        ),
                        onRatingUpdate: (double value) {},
                      )
                    : const SizedBox.shrink(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void showInvitePopup() {
    clearValidationMessage();
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) => InvitePopupDialog(
        userName: widget.userName ?? '',
        clubsLedByUser: clubsLedByUser,
        onCancel: () => context.pop(),
        onInvite: (bookClubId, invitationMsg) async {
          Future.delayed(
            const Duration(milliseconds: 500),
            () => context.pop(),
          );
          await confirmInviteRequest(bookClubId, invitationMsg);
        },
      ),
    );
  }

  void showNoClubsLedValidation(msg) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return CustomDialog(
          title: "Invite: ${widget.userName ?? ''}",
          message: msg,
          //"To send an invite you need to be a leader of a club. Start your own club to become a leader.",
          showDoneImage: false,
        );
      },
    );
  }

  Future<void> confirmInviteRequest(
      int bookClubId, String invitationMsg) async {
    String responseMessage = '';
    bool showDoneImg = false;
    final Map<String, dynamic> payload = {
      "bookClubId": bookClubId,
      "userId": widget.userId,
      "userType": ClubMemberType.member,
      "initiatedBy": loggedInUserId,
      "invitationMessage": invitationMsg
    };

    try {
      final responseMap =
          await Provider.of<BookClubController>(context, listen: false)
              .addMember(payload, context);

      if (responseMap["statusCode"] == 200) {
        responseMessage = GenericMessages.inviteRequestSuccess;
        showDoneImg = true;
      } else {
        responseMessage = responseMap['error'];
        showDoneImg = false;
      }
    } catch (e) {
      responseMessage = "Failed to submit request: $e";
    } finally {
      if (context.mounted) context.pop();
    }

    if (mounted) {
      showDialog(
        barrierColor: Colors.white60,
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return CustomDialog(
            title: "Invite: ${widget.userName ?? ''}",
            message: responseMessage,
            showDoneImage: showDoneImg,
          );
        },
      );
    }
  }
}
