import 'dart:developer';

import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/widgets/option_list_item.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/controller/leader_admin_controller.dart';
import 'package:eljunto/controller/message_controller.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/models/clubs_model/clubs_screen4_model/delete_club_model.dart';
import 'package:eljunto/models/clubs_model/clubs_screen4_model/incoming_outgoing_request.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../app/core/utils/text_style.dart';
import '../../reusableWidgets/previous_screen_appbar.dart';
import 'clubs_home/providers/clubs_home_provider.dart';

class LeaderAdminScreen extends StatefulWidget {
  final int? bookClubId;
  final String? buttonName;
  final bool clubFlag;

  const LeaderAdminScreen({
    super.key,
    this.bookClubId,
    this.buttonName,
    this.clubFlag = false,
  });

  @override
  State<LeaderAdminScreen> createState() => _LeaderAdminScreenState();
}

class _LeaderAdminScreenState extends State<LeaderAdminScreen> {
  BookClubModel? bookCaseModel;
  List<RequestManage>? incomingRequestList;
  BookClubController? bookClubController;
  MessageController? messageController;
  late ClubsHomeProvider provider;

  @override
  void initState() {
    provider = Provider.of<ClubsHomeProvider>(context, listen: false);
    messageController = Provider.of<MessageController>(context, listen: false);
    bookClubController =
        Provider.of<BookClubController>(context, listen: false);
    getClubDetails();
    super.initState();
    bookCaseModel =
        Provider.of<BookClubController>(context, listen: false).bookClubModel;
  }

  List<BookClubModel>? standingBookClubList;

  Future<void> getClubDetails() async {
    log("Book Club ID : ${widget.bookClubId}");
    await bookClubController
        ?.getBookClubs(
      '',
      null,
      widget.bookClubId,
      context,
      null,
      null,
    )
        .then((responseMap) async {
      if (responseMap["statusCode"] == 200) {
        List<BookClubModel> bookClubList = [];
        log("Response Map : ${responseMap["data"]}");
        if (responseMap["data"].isNotEmpty) {
          bookClubList = (responseMap["data"] as List)
              .map((item) => BookClubModel.fromJson(item))
              .toList();
          standingBookClubList = bookClubList;
          if (mounted) {
            bookClubController
                ?.updateData(standingBookClubList?[0] ?? BookClubModel());
          }
          log("Standing Book Club List : ${standingBookClubList?[0].bookClubId}");
        } else {
          standingBookClubList?.clear();
          standingBookClubList = [];
        }
      } else {}

      // await addStandingBookClubsToLocal();
      // loadMessagesFromDatabase();
    });
  }

  Future updateNotification() async {
    incomingRequestList = bookClubController?.incomingRequestList;
  }

  Future<void> updateCharterFunction() async {
    await Provider.of<LeaderAdminController>(context, listen: false)
        .updateClubCharter(
      context,
      bookCaseModel?.bookClubId,
      '',
      '',
      'CLOSED',
    )
        .then((value) async {
      if (value && mounted) {
        await Provider.of<ClubsHomeProvider>(context, listen: false)
            .refreshAllData(context);
        confirmDeleteFunction();
      }
    });
  }

  List<DeleteClubList> deleteClubList = [
    DeleteClubList(
      points:
          'The club’s message thread will be permanently closed and deleted.',
    ),
    DeleteClubList(
      points: 'The club will be removed from your profile',
    ),
    DeleteClubList(
      points: 'The club will cease to exist.',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: Consumer<BookClubController>(
              builder: (context, bookClubController, child) {
            return PreviousScreenAppBar(
              bookName: "Leader Admin",
              clubNameFlag: true,
              clubName: bookClubController.bookClubModel?.bookClubName,
              isSetProfile: true,
            );
          }),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              "assets/images/PaperBackground.png",
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: FutureBuilder(
            future: updateNotification(),
            builder: (context, snapShot) {
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Column(
                  children: [
                    const SizedBox(
                      height: 25,
                    ),
                    Stack(
                      clipBehavior: Clip.none,
                      children: [
                        OptionListItem(
                          label: 'Manage Incoming Requests',
                          onTap: () {
                            context.pushNamed(
                              'ManageIncomeRequest',
                              queryParameters: {
                                'bookClubId': widget.bookClubId.toString(),
                              },
                            );
                          },
                        ),
                        Positioned(
                          top: -8,
                          right: -3,
                          child: messageController?.hasNewStandingClubRequest ??
                                  false
                              ? Image.asset(
                                  AppConstants.notificationImagePath,
                                  height: 18,
                                  width: 18,
                                  filterQuality: FilterQuality.high,
                                  fit: BoxFit.cover,
                                )
                              : const SizedBox.shrink(),
                        ),
                      ],
                    ),
                    OptionListItem(
                      label: 'Manage Outgoing Invitations',
                      onTap: () {
                        context.pushNamed(
                          'ManageOutGInvitations',
                          queryParameters: {
                            'bookClubId': widget.bookClubId.toString(),
                          },
                          extra: 'Manage Outgoing Invitations',
                        );
                      },
                    ),
                    OptionListItem(
                      label: 'Manage Members',
                      onTap: () {
                        context.pushNamed(
                          'ManageMember',
                          queryParameters: {
                            'bookClubId': widget.bookClubId.toString(),
                          },
                          extra: 'Manage Members',
                        );
                      },
                    ),
                    OptionListItem(
                      label: 'Manage Meetings',
                      onTap: () {
                        context.pushNamed(
                          'ManageMeeting',
                          queryParameters: {
                            'bookClubId': widget.bookClubId.toString(),
                          },
                          extra: 'Manage Meetings',
                        );
                      },
                    ),
                    OptionListItem(
                      label: 'Charter & Member Request',
                      onTap: () {
                        context.pushNamed(
                          'CharterRequest',
                          queryParameters: {
                            'bookClubId': widget.bookClubId.toString(),
                          },
                          extra: 'Edit Charter & Member Request Prompt',
                        );
                      },
                    ),
                    OptionListItem(
                      label: 'Close Club',
                      textColor: AppConstants.redColor,
                      onTap: () => closeClubBox(),
                    ),
                  ],
                ),
              );
            }),
      ),
    );
  }

  void closeClubBox() {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () => context.pop(),
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Text(
                  "Close this Club",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        "Do you wish to close this club?",
                        textAlign: TextAlign.start,
                        style: lbRegular.copyWith(
                          fontSize: 12,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 10),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        "Club Closed:",
                        textAlign: TextAlign.center,
                        style: lbRegular.copyWith(
                          fontSize: 12,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 5),
                Column(
                  children: deleteClubList.map((e) {
                    return Padding(
                      padding: const EdgeInsets.only(left: 35, right: 20),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(top: 3.0),
                            child: Align(
                              alignment: Alignment.topCenter,
                              child: Text(
                                "•",
                                textAlign: TextAlign.start,
                                style: lbRegular.copyWith(
                                  fontSize: 12,
                                  height: 0.8,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 5),
                          Expanded(
                            child: Text(
                              textAlign: TextAlign.start,
                              e.points ?? '',
                              style: lbRegular.copyWith(
                                fontSize: 12,
                                height: 1.5,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      NetworkAwareTap(
                        onTap: () {
                          context.pop();
                          updateCharterFunction();
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.textGreenColor,
                          ),
                          child: Center(
                            child: Text(
                              "Delete",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                      NetworkAwareTap(
                        onTap: () => context.pop(),
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.backgroundColor,
                            border: Border.all(
                              color: AppConstants.primaryColor,
                              width: 1,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              "Cancel",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 30),
              ],
            )
          ],
        );
      },
    );
  }

  void confirmDeleteFunction() {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () async => await apiCall(),
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Text(
                      "Close this Club",
                      textAlign: TextAlign.center,
                      style: lbBold.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 28),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Text(
                    "The club has been closed",
                    textAlign: TextAlign.center,
                    style: lbRegular.copyWith(fontSize: 12),
                  ),
                ),
                const SizedBox(height: 25),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    NetworkAwareTap(
                      onTap: () async => await apiCall(),
                      child: Container(
                        height: 45,
                        width: MediaQuery.of(context).size.width / 3.5,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(49),
                          color: AppConstants.textGreenColor,
                        ),
                        child: Center(
                          child: Text(
                            "Ok",
                            textAlign: TextAlign.center,
                            style: lbBold.copyWith(fontSize: 18),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 30),
              ],
            )
          ],
        );
      },
    );
  }

  Future<void> apiCall() async {
    await Provider.of<ClubsHomeProvider>(context, listen: false)
        .refreshAllData(context);
    if (mounted) {
      final messageController = context.read<MessageController>();
      if (messageController.isFromChat) {
        context.pop();
        context.pushReplacementNamed('Message');
        messageController.setIsFromChat(false);
      } else {
        context.pop();
        context.goNamed('Clubs');
      }
    }
  }
}
