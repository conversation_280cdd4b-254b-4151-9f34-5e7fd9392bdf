import 'dart:developer';

import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../app/core/constants.dart';
import '../../app/core/utils/text_style.dart';
import '../../controller/book_club_controller.dart';
import '../../controller/search_controller.dart';
import '../../controller/user_controller.dart';
import '../../models/home_model/home_screen1_model/fellow_reader_model.dart';
import '../../models/search_model/search_model.dart';
import '../../reusableWidgets/cached_network_image.dart';
import '../../reusableWidgets/marquee_text.dart';
import '../../reusableWidgets/matches_bottom_sheet/matches_bottom_sheet.dart';
import '../../reusableWidgets/no_data_widget.dart';
import '../../reusableWidgets/previous_screen_appbar.dart';

class FellowReaderScreen extends StatefulWidget {
  const FellowReaderScreen({super.key});

  @override
  State<FellowReaderScreen> createState() => _FellowReaderScreenState();
}

class _FellowReaderScreenState extends State<FellowReaderScreen> {
  TextEditingController searchController = TextEditingController();

  int? loggedInUserId;
  bool isSearching = false;
  int offSet = 0;
  int limit = 10;
  String image = ApiConstants.imageBaseUrl;
  bool isSearcheable = false;
  String? bookName;
  SearchData? searchObj;
  List<Profile>? profileList;
  // List<FellowReader> fellowReadList = [];
  UserController? userController;
  bool isSearchLoading = false;
  bool isLoading = true;

  int fellowReadersLimit = 10;
  bool fellowReadersLoading = false;
  int fellowReadersCount = 0;
  final ScrollController _fellowScrollController = ScrollController();
  final ScrollController _scrollController = ScrollController();
  List<FellowReader> fellowReadList = [
    FellowReader(
      userName: 'Dummy',
      bookName: 'Dummy',
      bookAuthor: 'Dummy',
      userProfilePicture: AppConstants.clubOpeningZero,
    ),
    FellowReader(
      userName: 'Dummy',
      bookName: 'Dummy',
      bookAuthor: 'Dummy',
      userProfilePicture: AppConstants.clubOpeningZero,
    ),
    FellowReader(
      userName: 'Dummy',
      bookName: 'Dummy',
      bookAuthor: 'Dummy',
      userProfilePicture: AppConstants.clubOpeningZero,
    ),
  ];
  final _sessionManager = locator<SessionManager>();

  @override
  void initState() {
    userController = Provider.of<UserController>(context, listen: false);
    _fellowScrollController.addListener(_fellowonScroll);
    super.initState();
    _initializeUserId();
  }

  @override
  void dispose() {
    _fellowScrollController.removeListener(_fellowonScroll);
    _fellowScrollController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  List<int>? memberIdsList;
  Future<void> _initializeUserId() async {
    loggedInUserId = _sessionManager.userId;
    memberIdsList =
        Provider.of<BookClubController>(context, listen: false).memberIdsList;
    log("Fellow Reader Ids List : $memberIdsList");
    await getFellowReaders(false);
    isLoading = false;
    setState(() {});
  }

  void _fellowonScroll() {
    if (_fellowScrollController.position.pixels >=
            _fellowScrollController.position.maxScrollExtent &&
        !fellowReadersLoading &&
        fellowReadList.length < (userController?.fellowReadersCount ?? 0)) {
      // getNewClubOpenings(true);
      getFellowReaders(true); // Fetch more data
    }
  }

  Future<void> getFellowReaders(bool isMore) async {
    if (fellowReadList.length <= (userController?.fellowReadersCount ?? 0) ||
        !isMore) {
      fellowReadersLoading = true;

      if (isMore) {
        fellowReadersLimit += 10;
      }
    }
    Map<String, dynamic> payload = {
      "userId": loggedInUserId,
      "offset": 0,
      "limit": fellowReadersLimit,
    };

    await Provider.of<UserController>(context, listen: false)
        .getFellowReadersByUserId(payload, context)
        .then((responseMap) {
      fellowReadList.clear();
      fellowReadList = userController?.fellowReadList ?? [];
      for (var reader in memberIdsList ?? []) {
        fellowReadList.removeWhere((ids) {
          return ids.userId == reader;
        });
      }
    }).whenComplete(() {
      fellowReadersLoading = false;
    });
  }

  Future<SearchData> _fetchProfiles(String query, bool isMore) async {
    if (isMore) {
      limit += 10;
    }
    final searchObj =
        await Provider.of<SearchDataController>(context, listen: false)
            .searchFunction(query, loggedInUserId ?? 0, offSet, limit, context);

    return searchObj ?? SearchData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: const PreviousScreenAppBar(
            bookName: "Manage Outgoing Invitations",
            isSetProfile: true,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: Skeletonizer(
          effect: const SoldColorEffect(
            color: AppConstants.skeletonforgroundColor,
            lowerBound: 0.1,
            upperBound: 0.5,
          ),
          containersColor: AppConstants.skeletonBackgroundColor,
          enabled: isLoading,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 25,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: TextFormField(
                  controller: searchController,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                  ),
                  decoration: InputDecoration(
                    suffixIcon: NetworkAwareTap(
                      onTap: () {
                        (searchController.text.isEmpty)
                            ? const SizedBox.shrink()
                            : searchController.clear();
                        // searchObj = null;
                        // isSearching = false;
                        isSearchLoading = false;
                        setState(() {});
                      },
                      child: Icon(
                        searchController.text.isEmpty
                            ? Icons.search_rounded
                            : Icons.clear_rounded,
                        size: 25,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                    contentPadding: const EdgeInsets.all(10),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: const BorderSide(
                        color: AppConstants.primaryColor,
                        width: 1.5,
                      ),
                    ),
                    hintText: "Search",
                    hintStyle: lbRegular.copyWith(
                      fontSize: 18,
                      color: AppConstants.hintTextColor,
                    ),
                  ),
                  onChanged: (value) async {
                    if (searchController.text.length > 1) {
                      isSearchLoading = true;
                      searchObj = await _fetchProfiles(value, false);
                      profileList = searchObj?.profiles;
                      // fellowReadList.clear();
                    }
                    isSearcheable = true;
                    if ((searchObj?.profiles?.isEmpty ?? false)) {
                      isSearching = true;
                    }
                    // print("Search : $isSearching");
                    // }
                    if (isSearcheable && searchController.text.isEmpty) {
                      searchObj = null;
                      isSearching = false;
                      isSearchLoading = false;
                    }
                    setState(() {});
                  },
                ),
              ),
              const SizedBox(
                height: 12,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Text(
                  "Fellow Readers",
                  overflow: TextOverflow.ellipsis,
                  style: lbRegular.copyWith(
                    fontSize: 20,
                  ),
                ),
              ),
              const SizedBox(
                height: 12,
              ),
              if (isSearchLoading) ...[
                (profileList?.isNotEmpty ?? false)
                    ? Expanded(
                        child: ListView.builder(
                          // padding: const EdgeInsets.only(top: 12),
                          controller: _scrollController,
                          itemCount: profileList?.length ?? 0,
                          itemBuilder: (context, index) {
                            // optionSelectedIndex = index;
                            String? userImage;
                            // if (selectedIndex == 1) {
                            userImage = image +
                                (profileList?[index].userProfilePicture ?? '');
                            // }

                            return Container(
                              margin: const EdgeInsets.only(
                                  bottom: 25, top: 0, right: 20, left: 20),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                  color: AppConstants.primaryColor,
                                  width: 1.5,
                                ),
                              ),
                              child: ListTile(
                                onTap: () {
                                  context
                                      .pushNamed('club-member-profile', extra: {
                                    'userId': profileList?[index].userId,
                                    'userName': profileList?[index].userName,
                                  });
                                },
                                title: Text(
                                  profileList?[index].userName ?? '',
                                  style: lbBold.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                                subtitle: Padding(
                                  padding: const EdgeInsets.only(top: 5.0),
                                  child: Text(
                                    profileList?[index].userHandle ?? '',
                                    style: lbRegular.copyWith(
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                                trailing: CustomCachedNetworkImage(
                                  imageUrl: userImage,
                                  width: 45,
                                  height: 45,
                                  errorImage: AppConstants.profileLogoImagePath,
                                ),
                              ),
                            );
                          },
                        ),
                      )
                    : const NoDataWidget(
                        message: 'There is no profile with this name or handle',
                      )
                // : const SizedBox.shrink(),
              ] else ...[
                Expanded(
                  child: Consumer<UserController>(
                    builder: (context, userController, child) {
                      return ListView.builder(
                        controller: _fellowScrollController,
                        // padding: const EdgeInsets.only(left: 10, right: 20),
                        scrollDirection: Axis.vertical,
                        itemCount: fellowReadersLoading
                            ? fellowReadList.length + 1
                            : fellowReadList.length,
                        itemBuilder: (context, index) {
                          if (index == fellowReadList.length &&
                              fellowReadersLoading) {
                            return const Padding(
                              padding: EdgeInsets.only(left: 10.0),
                              child: Center(
                                child: CircularProgressIndicator(
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                            );
                          }
                          final userImage = image +
                              (fellowReadList[index].userProfilePicture ?? '');

                          return NetworkAwareTap(
                            onTap: () {
                              context.pushNamed(
                                'club-member-profile',
                                extra: {
                                  'userId': fellowReadList[index].userId,
                                  'userName': fellowReadList[index].userName
                                },
                              );
                            },
                            child: Skeleton.replace(
                              replacement:
                                  fellowReaderSkeleton(false, index, userImage),
                              child:
                                  fellowReaderSkeleton(true, index, userImage),
                            ),
                            // const SizedBox(
                            //   width: 10,
                            // ),
                          );
                        },
                      );
                    },
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget fellowReaderSkeleton(bool isBorder, int index, String userImage) {
    return Container(
      margin: const EdgeInsets.only(bottom: 25, top: 0, right: 20, left: 20),
      // margin: const EdgeInsets.only(bottom: 25),
      width: 320,
      height: 172,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? Colors.transparent
            : AppConstants.skeletonBackgroundColor,
        border: isBorder
            ? Border.all(
                color: AppConstants.primaryColor,
                width: 1.5,
              )
            : Border.all(
                color: Colors.transparent,
              ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 205,
                      child: Text(
                        fellowReadList[index].userName ?? '',
                        overflow: TextOverflow.ellipsis,
                        style: lbBold.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 3,
                    ),
                    Text(
                      fellowReadList[index].userLocation ?? '',
                      overflow: TextOverflow.ellipsis,
                      style: lbBold.copyWith(
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                ClipRRect(
                  borderRadius: BorderRadius.circular(49),
                  child: CustomCachedNetworkImage(
                    imageUrl: userImage,
                    width: 45,
                    height: 45,
                    errorImage: AppConstants.profileLogoImagePath,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 15,
            ),
            // if (fellowReadList[index].bookName?.isNotEmpty ?? false) ...[
            Text(
              (fellowReadList[index].bookName?.isNotEmpty ?? false)
                  ? "Currently Reading:"
                  : "No book in currently reading",
              overflow: TextOverflow.ellipsis,
              style: lbRegular.copyWith(
                fontSize: 12,
              ),
            ),
            const SizedBox(
              height: 15,
            ),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      MarqueeList(
                        children: [
                          Text(
                            fellowReadList[index].bookName ?? '',
                            overflow: TextOverflow.ellipsis,
                            style: lbBold.copyWith(
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 5,
                      ),
                      MarqueeList(
                        children: [
                          Text(
                            fellowReadList[index].bookAuthor ?? '',
                            overflow: TextOverflow.ellipsis,
                            style: lbRegular.copyWith(
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 15),
                NetworkAwareTap(
                  onTap: () {
                    /// SHOW BOTTOM SHEET
                    if (((fellowReadList[index].totalMatches ?? 0) > 0)) {
                      fellowReaderMatchesBottomSheet(
                        fellowReadList[index].currentlyReadingBooks ?? [],
                        fellowReadList[index].toBeReadBooks ?? [],
                        fellowReadList[index].fiveStarMatchBooks ?? [],
                        fellowReadList[index].userName ?? '',
                        fellowReadList[index].userId ?? 0,
                      );
                    } else {
                      log("NO MATCHES");
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      color: ((fellowReadList[index].totalMatches ?? 0) > 0)
                          ? AppConstants.textGreenColor
                          : AppConstants.isActiveRequestColor,
                    ),

                    /// ${fellowReadList[index].totalMatches ?? 0}
                    child: Text(
                      ((fellowReadList[index].totalMatches ?? 0) == 0)
                          ? "No Match"
                          : ((fellowReadList[index].totalMatches ?? 0) > 0 &&
                                  (fellowReadList[index].totalMatches ?? 0) < 2)
                              ? "${fellowReadList[index].totalMatches ?? 0} Match"
                              : "${fellowReadList[index].totalMatches ?? 0} Matches",
                      overflow: TextOverflow.ellipsis,
                      style: lbBold.copyWith(
                        fontSize: 12,
                        color: ((fellowReadList[index].totalMatches ?? 0) > 0)
                            ? AppConstants.primaryColor
                            : Colors.black38,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            // Text(
            //   fellowReadList[index].bookName ?? '',
            //   overflow: TextOverflow.ellipsis,
            //   style: lbBold.copyWith(
            //     fontSize: 12,
            //   ),
            // ),
            // const SizedBox(
            //   height: 5,
            // ),
            // Text(
            //   fellowReadList[index].bookAuthor ?? '',
            //   overflow: TextOverflow.ellipsis,
            //   style: lbRegular.copyWith(
            //     fontSize: 12,
            //   ),
            // ),
            // ] else ...[
            //   Text(
            //     "No book in currently reading",
            //     overflow: TextOverflow.ellipsis,
            //     style: lbRegular.copyWith(
            //       fontSize: 12,
            //     ),
            //   ),
            //   const Spacer(),
            //   Align(
            //     alignment: Alignment.bottomRight,
            //     child: NetworkAwareTap(
            //       onTap: () {
            //         /// SHOW BOTTOM SHEET
            //         if (((fellowReadList[index].totalMatches ?? 0) > 0)) {
            //           fellowReaderMatchesBottomSheet(
            //             fellowReadList[index].currentlyReadingBooks ?? [],
            //             fellowReadList[index].toBeReadBooks ?? [],
            //             fellowReadList[index].fiveStarMatchBooks ?? [],
            //             fellowReadList[index].userName ?? '',
            //             fellowReadList[index].userId ?? 0,
            //           );
            //         } else {
            //           log("NO MATCHES");
            //         }
            //       },
            //       child: Container(
            //         padding: const EdgeInsets.symmetric(
            //             horizontal: 16, vertical: 12),
            //         decoration: BoxDecoration(
            //           borderRadius: BorderRadius.circular(16),
            //           color: ((fellowReadList[index].totalMatches ?? 0) > 0)
            //               ? AppConstants.textGreenColor
            //               : AppConstants.isActiveRequestColor,
            //         ),

            //         /// ${fellowReadList[index].totalMatches ?? 0}
            //         child: Text(
            //           ((fellowReadList[index].totalMatches ?? 0) == 0)
            //               ? "No Match"
            //               : ((fellowReadList[index].totalMatches ?? 0) > 0 &&
            //                       (fellowReadList[index].totalMatches ?? 0) < 2)
            //                   ? "${fellowReadList[index].totalMatches ?? 0} Match"
            //                   : "${fellowReadList[index].totalMatches ?? 0} Matches",
            //           overflow: TextOverflow.ellipsis,
            //           style: lbBold.copyWith(
            //             fontSize: 12,
            //             color: ((fellowReadList[index].totalMatches ?? 0) > 0)
            //                 ? AppConstants.primaryColor
            //                 : Colors.black38,
            //           ),
            //         ),
            //       ),
            //     ),
            //   ),
            // ],
          ],
        ),
      ),
    );
  }

  Future<void> fellowReaderMatchesBottomSheet(
    List<int> cIds,
    List<int> toBeReadIds,
    List<int> starIds,
    String userName,
    int userId,
  ) async {
    return showModalBottomSheet(
      barrierColor: Colors.white60,
      isScrollControlled: true,
      useRootNavigator: true,
      useSafeArea: true,
      constraints: BoxConstraints(
        minHeight: MediaQuery.of(context).size.height * 0.3,
      ),
      context: context,
      builder: (context) {
        return FellowReaderMatchesBottomSheet(
          currentlyReadMatch: cIds,
          fivestarMatch: starIds,
          tobeReadMatch: toBeReadIds,
          userName: userName,
          userId: userId,
        );
      },
    );
  }
}
