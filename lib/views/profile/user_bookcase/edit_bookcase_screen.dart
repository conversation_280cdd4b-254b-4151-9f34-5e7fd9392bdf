import 'dart:developer';

import 'package:eljunto/app/core/enums/enums.dart';
import 'package:eljunto/app/core/helpers/data_helper.dart';
import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/controller/book_case_controller.dart';
import 'package:eljunto/models/book_case_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
// import 'package:eljunto/reusableWidgets/customDialouge_with_message.dart'; // No longer needed for this logic
import 'package:eljunto/reusableWidgets/custom_dropdown.dart';
import 'package:eljunto/reusableWidgets/no_data_widget.dart';
import 'package:eljunto/reusableWidgets/profile_appbar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../app/core/constants.dart';
import '../../../app/core/services/setup_locator.dart';
import '../../../models/profile_model/edit_bookcase/listof_book_model.dart';
import '../profile_home/services/profile_sync_service.dart';
import 'constants/bookcase_constants.dart' as constants;
import 'mixins/book_selection_mixin.dart';
import 'models/bookcase_screen_state.dart';
import 'widgets/add_edit_book_dialog.dart';
import 'widgets/book_card.dart';
import 'widgets/book_card_skeleton.dart';
import 'widgets/confirmation_dialogs.dart';
import 'widgets/read_review_dialog.dart';

/// Edit Bookcase Screen - Manages completed books with sorting and filtering
class EditBookCaseScreen extends StatefulWidget {
  final String? userName;
  final String? userHandler;
  final int? lengthofTopShelf;
  final bool? userClubInvitation;
  final String? userProfilePicture;

  const EditBookCaseScreen({
    super.key,
    this.userName,
    this.userHandler,
    this.lengthofTopShelf,
    this.userClubInvitation,
    this.userProfilePicture,
  });

  @override
  State<EditBookCaseScreen> createState() => _EditBookCaseScreenState();
}

class _EditBookCaseScreenState extends State<EditBookCaseScreen>
    with BookSelectionMixin {
  // Screen state management
  final BookcaseScreenState _screenState = BookcaseScreenState();

  // Controllers
  final roleController = TextEditingController();
  final reviewController = TextEditingController();
  final editReviewController = TextEditingController();
  final editCompleteDateController = TextEditingController();
  final monthyearController = TextEditingController();
  final unknownController = ValueNotifier<bool>(true);
  final selectTopShelfController = ValueNotifier<bool>(false);
  final _bookCaseScrollController = ScrollController();
  SuggestionsController<Books>? suggestionsController;

  // State variables
  bool monthyearValidation = false;
  bool istopShelfPopUpShow = false;
  bool ratingValidation = false;
  bool isInitialLoading = false;
  bool isPaginationLoading = false;
  bool alreadyExists = false;
  String responseMessage = '';
  bool _isMovable = false;

  // Data
  List<BookCaseModel>? completedBooks = [];
  List<BookCaseModel> currentlyReadingBooks = [];
  int completeBookcount = 0;
  int completeBookLimit = constants.BookcaseConstants.defaultLimit;
  int offset = constants.BookcaseConstants.defaultOffset;

  // Book operation state
  double ratingStar = 0;
  DateTime? selectedDate;
  String? readingCompleteDate;
  int? bookCaseId;

  // Add book popup state
  String? bookName;
  String? bookAuthor;
  int? bookId;
  List<Books> bookList = [];
  final bookController = TextEditingController();

  // Provider reference
  BookCaseController? bookCaseController;

  // Sorting options
  final List<BookCaseSortingOption> _sortingOptions = [
    BookCaseSortingOption.title,
    BookCaseSortingOption.completionDate,
    BookCaseSortingOption.author,
    BookCaseSortingOption.ratings
  ];
  final _sessionManager = locator<SessionManager>();

  @override
  void initState() {
    super.initState();
    _initializeScreen();
  }

  void _initializeScreen() {
    bookCaseController =
        Provider.of<BookCaseController>(context, listen: false);
    _bookCaseScrollController.addListener(_onScroll);
    roleController.text = BookCaseSortingOption.title.value;
    log("TopShelf Length : ${widget.lengthofTopShelf}");
    _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    setState(() => isInitialLoading = true);

    try {
      await _initializeUserId();
      await _loadBookCase(false);
    } catch (e) {
      log('Error loading initial data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text(constants.BookcaseConstants.loadBooksError)),
        );
      }
    } finally {
      if (mounted) {
        setState(() => isInitialLoading = false);
      }
    }
  }

  Future<void> _initializeUserId() async {
    _screenState.loggedInUserId = _sessionManager.userId;
  }

  void _onScroll() {
    if (_bookCaseScrollController.position.pixels >=
            _bookCaseScrollController.position.maxScrollExtent &&
        !isPaginationLoading &&
        (completedBooks?.length ?? 0) < completeBookcount) {
      _loadBookCase(true);
    }
  }

  Future<void> _loadBookCase(bool isLoadMore) async {
    if (isPaginationLoading) return;

    if (isLoadMore) {
      setState(() => isPaginationLoading = true);
      completeBookLimit += constants.BookcaseConstants.paginationIncrement;
    }

    try {
      final responseMap = await bookCaseController?.allBooksRead(
        _screenState.loggedInUserId ?? 0,
        completeBookLimit,
        offset,
        false,
        false,
        context,
      );

      if (responseMap?["statusCode"] == 200) {
        _processBookCaseResponse(responseMap!);
      }
    } catch (e) {
      log('Error loading bookcase: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text(constants.BookcaseConstants.loadBooksError)),
        );
      }
    } finally {
      if (mounted) {
        setState(() => isPaginationLoading = false);
      }
    }
  }

  void _processBookCaseResponse(Map<String, dynamic> responseMap) {
    final List<BookCaseModel> bookList = (responseMap["data"] as List)
        .map((item) => BookCaseModel.fromJson(item))
        .toList();

    completeBookcount = responseMap['count'];

    final result = DataHelper.getCurrentlyReadingAndTopShelfBooks(bookList);
    if (result.isNotEmpty) {
      currentlyReadingBooks = result[1];
      completedBooks = result[2];

      // Apply current sorting
      completedBooks = DataHelper.sortBookCaseList(
        completedBooks!,
        BookCaseSortingOption.values.firstWhere(
          (val) => val.value == roleController.text,
          orElse: () => BookCaseSortingOption.title,
        ),
      );
    }

    if (mounted) setState(() {});
  }

  Future<void> _sortBookCase(BookCaseSortingOption sortOption) async {
    if (completedBooks != null) {
      setState(() {
        completedBooks =
            DataHelper.sortBookCaseList(completedBooks!, sortOption);
      });
    }
  }

  @override
  void dispose() {
    _screenState.dispose();
    roleController.dispose();
    reviewController.dispose();
    editReviewController.dispose();
    editCompleteDateController.dispose();
    monthyearController.dispose();
    unknownController.dispose();
    selectTopShelfController.dispose();
    _bookCaseScrollController.dispose();
    bookController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return PreferredSize(
      preferredSize:
          const Size.fromHeight(constants.BookcaseConstants.appBarHeight),
      child: Container(
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(
              width: 1.5,
              color: AppConstants.primaryColor,
            ),
          ),
        ),
        child: Skeletonizer(
          effect: const SoldColorEffect(
            color: AppConstants.skeletonforgroundColor,
            lowerBound: 0.1,
            upperBound: 0.5,
          ),
          containersColor: AppConstants.textGreenColor,
          enabled: isInitialLoading,
          child: ProfileAppBar(
            userName: widget.userName,
            userHandle: widget.userHandler,
            isOpenToClubInvitation: widget.userClubInvitation ?? false,
            userOwnProfile: true,
            userProfilePicture: widget.userProfilePicture,
          ),
        ),
      ),
    );
  }

  Widget _buildBody() {
    return Container(
      height: MediaQuery.of(context).size.height,
      width: MediaQuery.of(context).size.width,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage(AppConstants.bgImagePath),
          filterQuality: FilterQuality.high,
          fit: BoxFit.fitWidth,
        ),
      ),
      child: Skeletonizer(
        effect: const SoldColorEffect(
          color: AppConstants.skeletonforgroundColor,
          lowerBound: 0.1,
          upperBound: 0.5,
        ),
        containersColor: AppConstants.skeletonBackgroundColor,
        enabled: isInitialLoading,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 25),
            _buildHeader(),
            const SizedBox(height: 25),
            _buildSortingAndAddSection(),
            _buildBooksList(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(
          horizontal: constants.BookcaseConstants.cardMarginHorizontal),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              "All Books Read",
              style: lbRegular.copyWith(fontSize: 20),
            ),
          ),
          Consumer<BookCaseController>(
            builder: (context, value, child) {
              return Text(
                "$completeBookcount-Books",
                overflow: TextOverflow.ellipsis,
                style: lbItalic.copyWith(fontSize: 20),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSortingAndAddSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(
          horizontal: constants.BookcaseConstants.cardMarginHorizontal),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildSortingDropdown(),
          _buildAddBookButton(),
        ],
      ),
    );
  }

  Widget _buildSortingDropdown() {
    return SizedBox(
      height: constants.BookcaseConstants.buttonHeight,
      width: MediaQuery.of(context).size.width / 2.3,
      child: CustomDropdownMenu(
        items: _sortingOptions,
        itemToString: (item) => item.value,
        controller: roleController,
        menuHeight: 200,
        onSelected: (value) {
          if (value != null) {
            _sortBookCase(value);
          }
        },
      ),
    );
  }

  Widget _buildAddBookButton() {
    return NetworkAwareTap(
      onTap: _showAddBookPopup,
      child: Text(
        constants.BookcaseConstants.addNewBook,
        style: lbItalic.copyWith(
          fontSize: 16,
          decoration: TextDecoration.underline,
        ),
      ),
    );
  }

  Widget _buildBooksList() {
    // Show skeleton during initial loading
    if (isInitialLoading) {
      return Expanded(
        child: GridView.builder(
          padding: const EdgeInsets.only(
            top: 25,
            bottom: 25,
            left: 20,
            right: 20,
          ),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 15,
            mainAxisSpacing: 15,
            childAspectRatio: 0.75,
          ),
          itemCount: 6, // Show 6 skeleton items
          itemBuilder: (context, index) => const BookCardSkeleton(),
        ),
      );
    }

    // Show actual content or empty state
    if (completedBooks?.isNotEmpty ?? false) {
      return Expanded(
        child: Column(
          children: [
            Expanded(
              child: GridView.builder(
                controller: _bookCaseScrollController,
                padding: const EdgeInsets.only(
                  bottom: 25,
                  left: 20,
                  right: 20,
                ),
                cacheExtent: constants.BookcaseConstants.listCacheExtent,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 15,
                  childAspectRatio: 0.75,
                ),
                itemCount: completedBooks?.length,
                itemBuilder: (context, index) {
                  final book = completedBooks?[index];
                  if (book == null) return const SizedBox.shrink();

                  return BookCard(
                    book: book,
                    onEdit: () => _showEditBookPopup(book, index),
                    onReadReview: () {
                      final bookName = book.bookName ?? '';
                      final bookAuthor = book.bookAuthor ?? '';
                      final review = book.review ?? '';
                      readReviewFunction(bookName, bookAuthor, review);
                    },
                  );
                },
              ),
            ),
            if (isPaginationLoading)
              Padding(
                padding: EdgeInsets.only(bottom: 20.0),
                child: _buildPaginationLoader(),
              ),
          ],
        ),
      );
    } else {
      // Show empty state
      return Padding(
        padding: EdgeInsets.only(top: 25.0),
        child: NoDataWidget(
          message: "No completed books found",
        ),
      );
    }
  }

  Widget _buildPaginationLoader() {
    return Container(
      alignment: Alignment.center,
      height: constants.BookcaseConstants.cardHeight,
      margin: const EdgeInsets.only(top: 25),
      child: const Center(
        child: CircularProgressIndicator(
          color: AppConstants.primaryColor,
        ),
      ),
    );
  }

  void _showAddBookPopup() {
    showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AddEditBookDialog(
          isEditMode: false,
          loggedInUserId: _screenState.loggedInUserId!,
          onConfirmAdd: (bookToAdd) async {
            final result = await bookCaseController?.addBookInBookCase(
                bookToAdd, this.context);

            if (result == 'exist') {
              final tempMessage = bookCaseController?.addBookErrorMessage ?? '';
              setState(() {
                responseMessage = tempMessage;
                alreadyExists = true;
                bookCaseId = bookCaseController?.bookCaseId;
                _isMovable = tempMessage.toLowerCase().contains('currently') &&
                    tempMessage.toLowerCase().contains("move it to");
              });
            } else {
              await _loadBookCase(false);
              _notifyCompletedBooksUpdate();
            }
            return result;
          },
          onConfirmUpdate: (_) async {
            // No-op in add mode
          },
          onDelete: () {
            // No-op in add mode
          },
        );
      },
    ).then((value) {
      if (value == 'exist') {
        _showConflictDialog();
      }
    });
  }

  void _showDeleteConfirmation(BookCaseModel book) {
    ConfirmationDialogs.showDeleteConfirmation(
      context: context,
      title: constants.BookcaseConstants.deleteBookTitle,
      message: constants.BookcaseConstants.deleteConfirmation,
      onConfirm: () => _confirmDelete(book.bookId),
    );
  }

  void _showEditBookPopup(BookCaseModel book, int index) {
    showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AddEditBookDialog(
          isEditMode: true,
          book: book,
          loggedInUserId: _screenState.loggedInUserId!,
          onConfirmAdd: (_) async => '', // No-op in edit mode
          onConfirmUpdate: (bookToUpdate) async {
            await Provider.of<BookCaseController>(this.context, listen: false)
                .updateBookCase(bookToUpdate, this.context);
            await _loadBookCase(false);
            _notifyCompletedBooksUpdate();
          },
          onDelete: () => _showDeleteConfirmation(book),
        );
      },
    );
  }

  Future<void> _confirmDelete(int? bookId) async {
    try {
      await Provider.of<BookCaseController>(context, listen: false)
          .deleteBook(bookId, context);
      await _loadBookCase(false);

      // Notify ProfileSyncService about completed books update
      _notifyCompletedBooksUpdate();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting book: $e')),
        );
      }
    }
  }

  /// Show read review popup
  Future<void> readReviewFunction(
    String? bookName,
    String? bookAuthor,
    String? review,
  ) async {
    await showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return ReadReviewDialog(
          bookName: bookName,
          bookAuthor: bookAuthor,
          review: review,
        );
      },
    );
  }

  /// Notify ProfileSyncService about completed books updates for cross-screen synchronization
  void _notifyCompletedBooksUpdate() {
    try {
      ProfileSyncService().onCompletedBooksUpdated(completedBooks ?? []);
      ProfileSyncService().requestRefresh();
      log('EditBookCaseScreen: Completed books update notification sent to ProfileSyncService');
    } catch (e) {
      log('EditBookCaseScreen: Error notifying completed books update - $e');
    }
  }

  // --- START: NEW METHODS FOR 'MOVE' FUNCTIONALITY ---

  /// Shows a dialog when trying to add a book that already exists.
  Future<void> _showConflictDialog() async {
    await showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10, bottom: 20),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                _buildSharedCloseButton(),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    children: [
                      Text(
                        "Add to Bookcase",
                        textAlign: TextAlign.center,
                        style: lbRegular.copyWith(fontSize: 18),
                      ),
                      const SizedBox(height: 25),
                      Text(
                        responseMessage,
                        textAlign: TextAlign.center,
                        style: lbRegular.copyWith(fontSize: 12),
                      ),
                      const SizedBox(height: 25),
                      if (_isMovable)
                        _buildMoveCancelButtons()
                      else
                        _buildOkButton(),
                      const SizedBox(height: 10),
                    ],
                  ),
                )
              ],
            )
          ],
        );
      },
    );
  }

  Widget _buildSharedCloseButton() {
    return NetworkAwareTap(
      onTap: () => context.pop(),
      child: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(top: 10, right: 10),
        child: Image.asset(
          AppConstants.closePopupImagePath,
          height: 30,
          width: 30,
        ),
      ),
    );
  }

  /// Builds the "Move" and "Cancel" buttons for the conflict dialog.
  Widget _buildMoveCancelButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        NetworkAwareTap(
          onTap: () async {
            await _handleMoveBook();
            if (mounted) context.pop();
          },
          child: Container(
            height: constants.BookcaseConstants.buttonHeight,
            width: MediaQuery.of(context).size.width / 3,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(
                  constants.BookcaseConstants.buttonBorderRadius),
              color: AppConstants.textGreenColor,
            ),
            child: Center(
              child: Text("Move", style: lbBold.copyWith(fontSize: 18)),
            ),
          ),
        ),
        NetworkAwareTap(
          onTap: () => context.pop(),
          child: Container(
            height: constants.BookcaseConstants.buttonHeight,
            width: MediaQuery.of(context).size.width / 3,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(
                  constants.BookcaseConstants.buttonBorderRadius),
              color: AppConstants.backgroundColor,
              border: Border.all(color: AppConstants.primaryColor),
            ),
            child: Center(
              child: Text(
                constants.BookcaseConstants.cancel,
                style: lbBold.copyWith(fontSize: 18),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Builds a single "OK" button for the conflict dialog.
  Widget _buildOkButton() {
    return NetworkAwareTap(
      onTap: () => context.pop(),
      child: Container(
        height: constants.BookcaseConstants.buttonHeight,
        width: MediaQuery.of(context).size.width / 3,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(
              constants.BookcaseConstants.buttonBorderRadius),
          color: AppConstants.textGreenColor,
        ),
        child: Center(
          child: Text("OK", style: lbBold.copyWith(fontSize: 18)),
        ),
      ),
    );
  }

  /// Handles the logic to "move" a book from currently reading to completed.
  Future<void> _handleMoveBook() async {
    final originalBook = bookCaseController?.originalBookModel;

    final bookToMove = originalBook != null
        ? originalBook.copyWith(
            is_currently_reading: false,
            bookCaseId: bookCaseId,
            toBeRead: false,
          )
        : BookCaseModel(
            userId: _screenState.loggedInUserId,
            bookId: bookCaseId,
            bookCaseId: bookCaseId,
            is_currently_reading: false,
          );

    try {
      await Provider.of<BookCaseController>(context, listen: false)
          .updateBookCase(bookToMove, context);

      await _loadBookCase(false);
      _notifyCompletedBooksUpdate();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error moving book: $e')),
        );
      }
    }
  }

  // --- END: NEW METHODS FOR 'MOVE' FUNCTIONALITY ---
}
