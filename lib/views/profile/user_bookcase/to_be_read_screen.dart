import 'dart:developer';

import 'package:eljunto/app/core/services/connectivity_service.dart';
import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../app/core/constants.dart';
import '../../../app/core/utils/text_style.dart';
import '../../../controller/book_case_controller.dart';
import '../../../controller/profile_controller.dart';
import '../../../models/book_case_model.dart';
import '../../../reusableWidgets/connection_error/network_aware_tap.dart';
import '../../../reusableWidgets/no_data_widget.dart';
import '../../../reusableWidgets/previous_screen_appbar.dart';
import '../profile_home/services/profile_sync_service.dart';
// Import shared components
import 'mixins/book_selection_mixin.dart';
import 'models/bookcase_screen_state.dart';
import 'widgets/add_book_popup.dart';
import 'widgets/book_list_item.dart';
import 'widgets/confirmation_dialogs.dart';

class ToBeReadScreen extends StatefulWidget {
  const ToBeReadScreen({super.key});

  @override
  State<ToBeReadScreen> createState() => _ToBeReadScreenState();
}

class _ToBeReadScreenState extends State<ToBeReadScreen>
    with BookSelectionMixin<ToBeReadScreen> {
  // Core state
  late BookcaseScreenState _screenState;
  List<BookCaseModel>? toBeReadList = [];
  bool toBeReadLoading = false;
  int toBeReadLimit = 10;
  int toBeReadcount = 0;

  // Controllers
  final _scrollController = ScrollController();

  // Provider controllers
  ProfileController? profileController;
  BookCaseController? bookCaseController;

  final _sessionManager = locator<SessionManager>();

  @override
  void initState() {
    super.initState();
    _screenState = BookcaseScreenState();

    // Use WidgetsBinding to ensure context is ready
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeControllers();
      _initializeUserId();
    });

    _scrollController.addListener(_onScrollToBeRead);
  }

  void _initializeControllers() {
    bookCaseController =
        Provider.of<BookCaseController>(context, listen: false);
    profileController = Provider.of<ProfileController>(context, listen: false);
  }

  @override
  void dispose() {
    _screenState.dispose();
    _scrollController.removeListener(_onScrollToBeRead);
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _initializeUserId() async {
    _screenState.loggedInUserId = _sessionManager.userId;
    await getToBeReadBook(false);
  }

  void _onScrollToBeRead() {
    if (BookcaseHelper.isAtBottom(_scrollController) &&
        BookcaseHelper.canLoadMore(
          currentLength: toBeReadList?.length ?? 0,
          totalCount: toBeReadcount,
          isLoading: toBeReadLoading,
        )) {
      ConnectivityService.networkClose(getToBeReadBook(true), context);
    }
  }

  Future<void> getToBeReadBook(bool isMore) async {
    if (!BookcaseHelper.canLoadMore(
          currentLength: toBeReadList?.length ?? 0,
          totalCount: toBeReadcount,
          isLoading: toBeReadLoading,
        ) &&
        isMore) {
      return;
    }

    try {
      toBeReadLoading = true;
      if (mounted) setState(() {});

      if (isMore) {
        toBeReadLimit += BookcaseConstants.paginationIncrement;
      }

      final responseMap = await bookCaseController?.getToBeReadBook(
          _screenState.loggedInUserId ?? 0, toBeReadLimit, 0, context);

      if (!mounted) return;

      if (responseMap!["statusCode"] == 200) {
        toBeReadcount = responseMap['count'] ?? 0;

        if (responseMap["data"] != null) {
          final bookCaseList = (responseMap["data"] as List)
              .map((item) => BookCaseModel.fromJson(item))
              .toList();
          toBeReadList = bookCaseList;

          if (mounted) {
            setState(() {});
          }
        } else {
          toBeReadList?.clear();
          toBeReadList = [];
        }
      } else if (responseMap['statusCode'] == 404) {
        toBeReadList?.clear();
        toBeReadList = [];
        toBeReadcount = 0;
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(
                    'Failed to load books: ${responseMap["message"] ?? "Unknown error"}')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(BookcaseHelper.getSafeErrorMessage(e))),
        );
      }
    } finally {
      toBeReadLoading = false;
      if (mounted) setState(() {});
    }
  }

  Future<bool> confirmAddBook(bool isMove) async {
    try {
      if (isMove) {
        final updatedBook = BookCaseModel(
          bookCaseId: _screenState.bookId,
          userId: _screenState.loggedInUserId,
          bookId: _screenState.bookId,
          is_currently_reading: true,
          toBeRead: false,
          reRead: 0,
        );

        await bookCaseController?.updateBookCase(updatedBook, context);

        // Refresh the local list first to include the newly added book
        await getToBeReadBook(false);

        // Notify ProfileSyncService about to-be-read and currently reading updates
        _notifyToBeReadUpdate();
        return false;
      } else {
        final addBook = BookCaseModel(
          bookId: _screenState.bookId,
          toBeRead: true,
          bookAuthor: _screenState.bookAuthor,
          bookName: _screenState.bookName,
        );

        final result =
            await bookCaseController?.addBookInBookCase(addBook, context);

        if (result == 'exist') {
          _screenState.alreadyExists = true;
          _screenState.responseMessage =
              bookCaseController?.addBookErrorMessage ?? "";
          return true;
        } else {
          _screenState.alreadyExists = false;
          // Refresh the local list first to include the newly added book
          await getToBeReadBook(false);
          // Notify ProfileSyncService about to-be-read update
          _notifyToBeReadUpdate();
          return false;
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(BookcaseHelper.getSafeErrorMessage(e))),
        );
      }
      return false;
    }
  }

  Future<void> confirmMoveBook(int bookCaseId) async {
    try {
      final movingBook = BookCaseModel(
        bookCaseId: bookCaseId,
        userId: _screenState.loggedInUserId,
        bookId: _screenState.bookId,
        is_currently_reading: true,
        toBeRead: false,
        reRead: 0,
      );

      await bookCaseController?.updateBookCase(movingBook, context);

      // Refresh the local list first to include the newly added book
      await getToBeReadBook(false);

      // Notify ProfileSyncService about to-be-read and currently reading updates
      _notifyToBeReadUpdate();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(BookcaseHelper.getSafeErrorMessage(e))),
        );
      }
    }
  }

  Future<void> deleteBook(int? bookId) async {
    try {
      await bookCaseController?.deleteBook(bookId, context);
      await getToBeReadBook(false);

      // Notify ProfileSyncService about to-be-read update
      _notifyToBeReadUpdate();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(BookcaseHelper.getSafeErrorMessage(e))),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: const PreviousScreenAppBar(
            bookName: "Edit To-Be-Read",
            isSetProfile: true,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(AppConstants.bgImagePath),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: Skeletonizer(
          effect: const SoldColorEffect(
            color: AppConstants.skeletonforgroundColor,
            lowerBound: 0.1,
            upperBound: 0.5,
          ),
          containersColor: AppConstants.skeletonBackgroundColor,
          enabled: toBeReadLoading,
          child: Column(
            children: [
              const SizedBox(height: 25),
              _buildAddBookButton(),
              _buildBooksList(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddBookButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          NetworkAwareTap(
            onTap: () => _showAddBookPopup(),
            child: Text(
              BookcaseConstants.addNewBook,
              style: lbItalic.copyWith(
                fontSize: 16,
                decoration: TextDecoration.underline,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBooksList() {
    if (toBeReadLoading) {
      return Expanded(
        child: ListView.builder(
          itemCount: 3,
          itemBuilder: (context, index) => BookListItem(
            book: BookCaseModel(
              bookName: 'Loading...',
              bookAuthor: 'Loading...',
            ),
            showSkeleton: true,
            type: BookListItemType.toBeRead,
            editText: 'Loading...',
            moveText: 'Loading...',
          ),
        ),
      );
    }
    if (toBeReadList?.isNotEmpty ?? false) {
      return Expanded(
        child: ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.only(bottom: 25),
          cacheExtent: 500,
          // Preload items for smoother scrolling
          itemCount: toBeReadList?.length ?? 0,
          itemBuilder: (context, index) {
            final book = toBeReadList?[index];
            if (book == null) return const SizedBox.shrink();

            return BookListItem(
              book: book,
              type: BookListItemType.toBeRead,
              moveText: BookcaseConstants.moveToCurrentlyReading,
              onMove: () => _showMoveConfirmation(index, book),
              onDelete: () => _showDeleteConfirmation(index, book),
            );
          },
        ),
      );
    } else {
      return const Padding(
        padding: EdgeInsets.only(top: 25.0),
        child: NoDataWidget(
          message: BookcaseConstants.noToBeRead,
        ),
      );
    }
  }

  void _showAddBookPopup() {
    showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AddBookPopup(
              title: "Add New Book",
              isCurrentlyReading: false,
              loggedInUserId: _screenState.loggedInUserId,
              onAddBook: confirmAddBook,
              onBookAdded: () => getToBeReadBook(false),
              onBookSelected: (bookId, bookCaseId, bookname, bookAuthor) =>
                  _screenState.updateBookSelection(
                id: bookId,
                caseId: bookCaseId,
                name: bookname,
                author: bookAuthor,
                idNotEmpty: true,
              ),
            );
          },
        );
      },
    );
  }

  void _showDeleteConfirmation(int index, BookCaseModel book) {
    ConfirmationDialogs.showDeleteConfirmation(
      context: context,
      title: "Delete Book:",
      message: "Are you sure you want to delete this book?",
      onConfirm: () => deleteBook(book.bookId),
    );
  }

  void _showMoveConfirmation(int index, BookCaseModel book) {
    ConfirmationDialogs.showActionConfirmation(
      context: context,
      message:
          "Are you sure you want to move this book to your currently reading list?",
      confirmText: "Move",
      onConfirm: () async {
        _screenState.bookId = book.bookId;
        await confirmMoveBook(book.bookCaseId!);
        await getToBeReadBook(false);
      },
    );
  }

  /// Notify ProfileSyncService about to-be-read updates for cross-screen synchronization
  void _notifyToBeReadUpdate() {
    try {
      ProfileSyncService().onToBeReadUpdated(toBeReadList ?? []);
      ProfileSyncService().requestRefresh();
      log('ToBeReadScreen: To-be-read update notification sent to ProfileSyncService');
    } catch (e) {
      log('ToBeReadScreen: Error notifying to-be-read update - $e');
    }
  }
}
