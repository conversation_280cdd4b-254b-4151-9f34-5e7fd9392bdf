import 'dart:developer';

import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/models/club_charter_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/custom_button.dart';
import 'package:eljunto/reusableWidgets/previous_screen_appbar.dart';
import 'package:eljunto/views/settings/provider/setting_provider.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class DeleteAccountPage extends StatefulWidget {
  const DeleteAccountPage({super.key});

  @override
  State createState() => _DeleteAccountPageState();
}

class _DeleteAccountPageState extends State<DeleteAccountPage> {
  int? loggedinUserId;
  final _sessionManager = locator<SessionManager>();

  @override
  void initState() {
    _initializeUserId();
    super.initState();
  }

  Future<void> _initializeUserId() async {
    loggedinUserId = _sessionManager.userId;
  }

  List<ClubCharterModel> information = [
    ClubCharterModel(
      rules: "Are you sure you want to permanently delete your account?",
    ),
    ClubCharterModel(
      rules: "This is irreversible.",
    ),
    ClubCharterModel(
      rules:
          "Deleting your account does not cancel a paid El Junto Subscription. You must manage your paid subscription through the platform or app store that you originally used to subscribe.",
    ),
    ClubCharterModel(
      rules:
          "All of you information will be deleted, and you will need to create a new account and resubscribe under that new account to use this app again.",
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: const PreviousScreenAppBar(
            bookName: 'Delete Account',
            isSetProfile: true,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: ListView(
          children: [
            const SizedBox(height: 10),
            ...information.map((policy) {
              return Padding(
                padding: const EdgeInsets.only(left: 20, right: 20, top: 15),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 3.0),
                      child: Align(
                        alignment: Alignment.topCenter,
                        child: Text(
                          "•",
                          textAlign: TextAlign.start,
                          style: lbRegular.copyWith(
                            fontSize: 20,
                            height: 0.8,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 5),
                    Expanded(
                      child: Text(
                        textAlign: TextAlign.start,
                        policy.rules ?? '',
                        style: lbRegular.copyWith(fontSize: 14),
                      ),
                    ),
                  ],
                ),
              );
            }),
            const SizedBox(height: 25),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: CustomButton(
                text: "Delete Account",
                onPressed: handleDeleteAccount,
              ),
            )
          ],
        ),
      ),
    );
  }

  void handleDeleteAccount() {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.symmetric(horizontal: 20),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Consumer<SettingProvider>(
              builder: (context, provider, child) {
                return Column(
                  children: [
                    NetworkAwareTap(
                      onTap: () {
                        context.pop();
                      },
                      child: Container(
                        alignment: Alignment.centerRight,
                        padding: const EdgeInsets.only(top: 10),
                        child: Image.asset(
                          AppConstants.closePopupImagePath,
                          height: 30,
                          width: 30,
                        ),
                      ),
                    ),
                    Text(
                      "Just a final check",
                      textAlign: TextAlign.center,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                    const SizedBox(
                      height: 25,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 30, right: 20),
                      child: Text(
                        "Are you sure you wish to delete your account?",
                        textAlign: TextAlign.center,
                        style: lbRegular.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                    provider.errorMessage.isNotEmpty
                        ? const SizedBox(
                            height: 10,
                          )
                        : const SizedBox.shrink(),
                    provider.errorMessage.isNotEmpty
                        ? Padding(
                            padding:
                                const EdgeInsets.only(left: 30.0, right: 20),
                            child: Align(
                              alignment: Alignment.topLeft,
                              child: Text(
                                provider.errorMessage,
                                style: lbBold.copyWith(
                                  fontSize: 14,
                                  color: AppConstants.redColor,
                                ),
                              ),
                            ),
                          )
                        : const SizedBox.shrink(),
                    const SizedBox(
                      height: 25,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 30.0),
                      child: Row(
                        mainAxisAlignment: provider.deleteLoading
                            ? MainAxisAlignment.center
                            : MainAxisAlignment.spaceBetween,
                        children: [
                          CustomLoaderButton(
                            buttonWidth: provider.deleteLoading
                                ? 45.0
                                : MediaQuery.of(context).size.width / 3.2,
                            buttonRadius: 30.0,
                            buttonChild: provider.deleteLoading
                                ? const CircularProgressIndicator(
                                    valueColor:
                                        AlwaysStoppedAnimation(Colors.white),
                                    strokeWidth: 3.0,
                                  )
                                : Text(
                                    'Yes',
                                    style: lbBold.copyWith(
                                      fontSize: 18,
                                      color: AppConstants.primaryColor,
                                    ),
                                  ),
                            buttonPressed: () async {
                              provider
                                  .confirmDeleteAccount(
                                      loggedinUserId ?? 0, context)
                                  .then((value) async {
                                if (value) {
                                  if (context.mounted) {
                                    SharedPreferences pref =
                                        await SharedPreferences.getInstance();
                                    await pref.clear();
                                    log('Token expire');
                                    if (context.mounted) {
                                      context.goNamed('login');
                                    }
                                  }
                                } else {}
                              });
                            },
                          ),
                          const SizedBox(width: 10),
                          !provider.deleteLoading
                              ? NetworkAwareTap(
                                  onTap: () {
                                    context.pop();
                                  },
                                  child: Container(
                                    height: 45,
                                    width:
                                        MediaQuery.of(context).size.width / 3.2,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(49),
                                      color: AppConstants.backgroundColor,
                                      border: Border.all(
                                        color: AppConstants.primaryColor,
                                      ),
                                    ),
                                    child: Center(
                                      child: Text(
                                        "Cancel",
                                        textAlign: TextAlign.center,
                                        style: lbBold.copyWith(
                                          fontSize: 18,
                                        ),
                                      ),
                                    ),
                                  ),
                                )
                              : const SizedBox.shrink(),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 30,
                    ),
                  ],
                );
              },
            ),
          ],
        );
      },
    );
  }
}
