// import 'dart:developer';
//
// import 'package:eljunto/app/core/constants.dart';
// import 'package:eljunto/app/core/services/setup_locator.dart';
// import 'package:eljunto/app/core/utils/text_style.dart';
// import 'package:eljunto/controller/user_controller.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_advanced_switch/flutter_advanced_switch.dart';
// import 'package:provider/provider.dart';
// import 'package:skeletonizer/skeletonizer.dart';
//
// import '../../app/core/services/session_manager.dart' show SessionManager;
// import '../../models/notification_settings_model/notification_settings_model.dart';
// import '../../reusableWidgets/previous_screen_appbar.dart';
//
// final Map<int, String> notificationTypeTitles = {
//   1: 'New Club Meeting',
//   2: 'Meeting Time Change',
//   3: 'Club Meeting Reminders',
//   4: 'New Invitations',
//   5: 'Club Requests\n(For Club Leaders)',
//   6: 'New Club Messages',
// };
//
// /// A page that displays and manages notification settings
// class NotificationSettingsPage extends StatefulWidget {
//   const NotificationSettingsPage({super.key});
//
//   @override
//   State<NotificationSettingsPage> createState() =>
//       _NotificationSettingsPageState();
// }
//
// class _NotificationSettingsPageState extends State<NotificationSettingsPage> {
//   static const double _headerHeight = 80.0;
//   static const double _horizontalPadding = 20.0;
//   static const double _verticalSpacing = 8.0;
//   static const double _toggleSpacing = 80.0;
//   static const double _endSpacing = 30.0;
//
//   List<NotificationSetting> _settings = [];
//   int? loginUserId;
//   bool _isLoading = false;
//
//   final _sessionManager = locator<SessionManager>();
//
//   @override
//   void initState() {
//     super.initState();
//     _initializeData();
//   }
//
//   Future<void> _initializeData() async {
//     loginUserId = _sessionManager.userId;
//     log("User ID: $loginUserId");
//     if (mounted) {
//       await _fetchNotificationSettings();
//     }
//   }
//
//   Future<void> _fetchNotificationSettings() async {
//     try {
//       setState(() => _isLoading = true);
//       if (loginUserId == null) return;
//
//       final responseMap = await Provider.of<UserController>(
//         context,
//         listen: false,
//       ).getNotificationSettings(loginUserId!, context);
//
//       if (responseMap.containsKey('error')) {
//         log(responseMap['error']);
//       } else if (mounted) {
//         setState(() {
//           _settings = _parseNotificationSettings(responseMap);
//         });
//       }
//     } catch (e) {
//       log('Error fetching notification settings: $e');
//     } finally {
//       setState(() => _isLoading = false);
//     }
//   }
//
//   List<NotificationSetting> _parseNotificationSettings(
//       Map<String, dynamic> response) {
//     return (response['data'] as List).map<NotificationSetting>((item) {
//       final type = item['notificationType'] as int;
//       final title = notificationTypeTitles[type] ?? 'Unknown Notification';
//       final email = item['emailNotification'] == 1;
//       final os = item['osNotification'] == 1;
//
//       return NotificationSetting(title, email, os, true, true);
//     }).toList();
//   }
//
//   Widget _buildHeader() {
//     return Padding(
//       padding: const EdgeInsets.symmetric(
//         horizontal: _horizontalPadding,
//         vertical: _verticalSpacing,
//       ),
//       child: Row(
//         children: [
//           const Spacer(),
//           Text('Email', style: lbRegular.copyWith(fontSize: 14)),
//           const SizedBox(width: _toggleSpacing),
//           Text('OS', style: lbRegular.copyWith(fontSize: 14)),
//           const SizedBox(width: _endSpacing),
//         ],
//       ),
//     );
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: PreferredSize(
//         preferredSize: const Size.fromHeight(_headerHeight),
//         child: Container(
//           decoration: const BoxDecoration(
//             border: Border(
//               bottom: BorderSide(
//                 width: 1.5,
//                 color: AppConstants.primaryColor,
//               ),
//             ),
//           ),
//           child: const PreviousScreenAppBar(
//             bookName: 'Notification Settings',
//             isSetProfile: true,
//           ),
//         ),
//       ),
//       body: Container(
//         height: MediaQuery.of(context).size.height,
//         width: MediaQuery.of(context).size.width,
//         decoration: const BoxDecoration(
//           image: DecorationImage(
//             image: AssetImage(AppConstants.bgImagePath),
//             filterQuality: FilterQuality.high,
//             fit: BoxFit.cover,
//           ),
//         ),
//         child: Skeletonizer(
//           effect: const SoldColorEffect(
//             color: AppConstants.skeletonforgroundColor,
//             lowerBound: 0.1,
//             upperBound: 0.5,
//           ),
//           enabled: _isLoading,
//           child: Column(
//             children: [
//               const SizedBox(height: 25),
//               _buildHeader(),
//               Expanded(
//                 child: Skeleton.replace(
//                   replace: _isLoading,
//                   replacement: ListView.builder(
//                     itemCount: 5,
//                     itemBuilder: (context, index) =>
//                         const NotificationSettingSkeleton(),
//                   ),
//                   child: ListView.builder(
//                     itemCount: _settings.length,
//                     itemBuilder: (context, index) => NotificationSettingRow(
//                       setting: _settings[index],
//                       userId: loginUserId,
//                       index: index,
//                     ),
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
//
// class NotificationSettingRow extends StatelessWidget {
//   static const double _switchWidth = 75.0;
//   static const double _switchHeight = 35.0;
//   static const double _switchBorderRadius = 50.0;
//   static const double _thumbSize = 10.0;
//   static const double _textSize = 12.0;
//   static const double _switchTextSize = 12.0;
//
//   final NotificationSetting setting;
//   final int? index;
//   final int? userId;
//
//   const NotificationSettingRow({
//     super.key,
//     required this.setting,
//     this.index,
//     this.userId,
//   });
//
//   int? _getNotificationTypeKey(String title) {
//     return notificationTypeTitles.entries
//         .firstWhere(
//           (entry) => entry.value == title,
//           orElse: () => const MapEntry(-1, ''),
//         )
//         .key;
//   }
//
//   Widget _buildSwitch({
//     required ValueNotifier<bool> controller,
//     required ValueChanged<bool> onChanged,
//     required bool isEmail,
//   }) {
//     return AdvancedSwitch(
//       controller: controller,
//       width: _switchWidth,
//       height: _switchHeight,
//       initialValue: controller.value,
//       thumb: ValueListenableBuilder<bool>(
//         valueListenable: controller,
//         builder: (context, value, _) {
//           return Container(
//             height: _thumbSize,
//             decoration: BoxDecoration(
//               color: Colors.white,
//               border: Border.all(color: AppConstants.primaryColor),
//               borderRadius: BorderRadius.circular(_switchBorderRadius),
//             ),
//           );
//         },
//       ),
//       borderRadius: BorderRadius.circular(20),
//       activeChild: Text(
//         "ON",
//         style: lbBold.copyWith(fontSize: _switchTextSize),
//       ),
//       inactiveChild: Text(
//         "OFF",
//         style: lbBold.copyWith(fontSize: _switchTextSize),
//       ),
//       activeColor: AppConstants.textGreenColor,
//       inactiveColor: Colors.transparent,
//       onChanged: (value) => onChanged(value),
//     );
//   }
//
//   Future<void> _handleUpdateNotificationSetting(
//     Map<String, dynamic> payload,
//     BuildContext context,
//   ) async {
//     final userController = Provider.of<UserController>(context, listen: false);
//     final success = await userController.controlNotification(payload);
//
//     if (!success && context.mounted) {
//       final errorMessage = userController.apiResMessage;
//       _showErrorDialog(errorMessage, context);
//     }
//   }
//
//   Map<String, dynamic> _createNotificationPayload(
//       bool emailValue, bool osValue) {
//     return {
//       "userId": userId,
//       "notificationTypeKey": _getNotificationTypeKey(setting.title).toString(),
//       "emailNotification": emailValue ? 1 : 0,
//       "osNotification": osValue ? 1 : 0,
//     };
//   }
//
//   void _showErrorDialog(String message, BuildContext context) {
//     showDialog(
//       context: context,
//       barrierDismissible: false,
//       builder: (context) => AlertDialog(
//         content: Text(message),
//         actions: [
//           TextButton(
//             onPressed: () => Navigator.pop(context),
//             child: const Text('OK'),
//           ),
//         ],
//       ),
//     );
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 8.0),
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.end,
//         children: [
//           Expanded(
//             child: Text(
//               setting.title,
//               style: lbRegular.copyWith(fontSize: _textSize),
//             ),
//           ),
//           if (setting.showEmailController &&
//               setting.title != 'New Club Messages')
//             Expanded(
//               child: Align(
//                 alignment: Alignment.center,
//                 child: Container(
//                   decoration: BoxDecoration(
//                     borderRadius: BorderRadius.circular(_switchBorderRadius),
//                     border: Border.all(color: AppConstants.primaryColor),
//                   ),
//                   child: _buildSwitch(
//                     controller: setting.emailController,
//                     isEmail: true,
//                     onChanged: (value) {
//                       final payload = _createNotificationPayload(
//                         value,
//                         setting.osController.value,
//                       );
//                       _handleUpdateNotificationSetting(payload, context);
//                     },
//                   ),
//                 ),
//               ),
//             ),
//           Container(
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(_switchBorderRadius),
//               border: Border.all(color: AppConstants.primaryColor),
//             ),
//             child: _buildSwitch(
//               controller: setting.osController,
//               isEmail: false,
//               onChanged: (value) {
//                 final payload = _createNotificationPayload(
//                   setting.emailController.value,
//                   value,
//                 );
//                 _handleUpdateNotificationSetting(payload, context);
//               },
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
//
// class NotificationSettingSkeleton extends StatelessWidget {
//   const NotificationSettingSkeleton({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 12.0),
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           // Title placeholder
//           Container(
//             width: 150,
//             height: 16,
//             decoration: BoxDecoration(
//               color: Colors.grey[300],
//               borderRadius: BorderRadius.circular(4),
//             ),
//           ),
//
//           // Toggle switches placeholders
//           Row(
//             children: [
//               // Email toggle placeholder
//               Container(
//                 width: 75,
//                 height: 35,
//                 margin: const EdgeInsets.only(right: 20),
//                 decoration: BoxDecoration(
//                   color: Colors.grey[200],
//                   borderRadius: BorderRadius.circular(20),
//                 ),
//               ),
//
//               // OS toggle placeholder
//               Container(
//                 width: 75,
//                 height: 35,
//                 decoration: BoxDecoration(
//                   color: Colors.grey[200],
//                   borderRadius: BorderRadius.circular(20),
//                 ),
//               ),
//             ],
//           ),
//         ],
//       ),
//     );
//   }
// }
