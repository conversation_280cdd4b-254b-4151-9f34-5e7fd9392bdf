import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/controller/login_controller.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/custom_button.dart';
import 'package:eljunto/reusableWidgets/custom_text_widget.dart';
import 'package:eljunto/reusableWidgets/previous_screen_appbar.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

class ChangePasswordPage extends StatefulWidget {
  const ChangePasswordPage({super.key});
  @override
  State createState() => _ChangePasswordPageState();
}

class _ChangePasswordPageState extends State<ChangePasswordPage> {
  final _passwordFormKey = GlobalKey<FormState>();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _newPasswordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  String? userMailId;

  final bool _isPassWordCorrect = false;
  bool _isPasswordValid = false;
  bool _isPasswordComplex = false;
  bool _isPasswordMatch = false;
  bool showPassword = true;
  bool showNewPassword = true;
  bool showConfirmPassword = true;
  bool isTempPasswordSent = false;
  String confirmationMessage = "";
  String errorMessage = "";
  bool isLoading = false;
  final _sessionManager = locator<SessionManager>();

  @override
  void initState() {
    super.initState();
    localData();
  }

  Future<void> localData() async {
    userMailId = _sessionManager.userEmail;
  }

  void _submitForm() async {
    bool validation = _passwordFormKey.currentState!.validate();
    if (validation &&
        _passwordController.text.isNotEmpty &&
        _newPasswordController.text.isNotEmpty &&
        _confirmPasswordController.text.isNotEmpty &&
        _newPasswordController.text.length > 7 &&
        _confirmPasswordController.text.length > 7) {
      isLoading = true;
      errorMessage = "";
      setState(() {});
      final loginController =
          Provider.of<LoginController>(context, listen: false);
      await loginController
          .changePassword(userMailId ?? '', _passwordController.text,
              _newPasswordController.text, _confirmPasswordController.text)
          .then((value) {
        setState(() {
          isLoading = false;
        });
        if (value) {
          handleConfirmChangePassword();
        } else {
          setState(() {
            errorMessage = loginController.changePasswordErrorMessage ?? '';
          });
        }
      });
    } else {}
  }

  void _validateNewPassword(String password) {
    setState(() {
      newPassFlag = false;
      _isPasswordValid = password.length >= 8;
      _isPasswordComplex =
          RegExp(r'(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[\W_]).{8,}')
              .hasMatch(password);
      _isPasswordMatch = password == _confirmPasswordController.text;
    });
  }

  void _validateConfirmPassword(String confirmPassword) {
    setState(() {
      retypePassFlag = false;
      _isPasswordMatch = confirmPassword == _newPasswordController.text;
    });
  }

  @override
  void dispose() {
    _passwordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  bool currentPassFlag = false;
  bool newPassFlag = false;
  bool retypePassFlag = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(80),
          child: Container(
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  width: 1.5,
                  color: AppConstants.primaryColor,
                ),
              ),
            ),
            child: const PreviousScreenAppBar(
              bookName: 'Change Password',
              isSetProfile: true,
            ),
          ),
        ),
        body: Stack(
          children: [
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Container(
                height: MediaQuery.of(context).size.height,
                width: MediaQuery.of(context).size.width,
                decoration: const BoxDecoration(
                  // color: Colors.white,
                  image: DecorationImage(
                    image: AssetImage(
                      AppConstants.bgImagePath,
                    ),
                    filterQuality: FilterQuality.high,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            Form(
              key: _passwordFormKey,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: ListView(
                  physics: const PageScrollPhysics(),
                  children: [
                    const SizedBox(height: 25),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "Current Password:",
                          style: lbRegular.copyWith(
                            fontSize: 18,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    SizedBox(
                      child: TextFormField(
                        controller: _passwordController,
                        style: lbRegular.copyWith(
                          fontSize: 18,
                        ),
                        decoration: InputDecoration(
                          contentPadding: const EdgeInsets.all(10),
                          filled: true,
                          fillColor: Colors.white.withValues(alpha: 0.8),
                          errorStyle: errorMsg,
                          border: const OutlineInputBorder(),
                          suffixIcon: IconButton(
                            icon: Icon(
                              showPassword
                                  ? Icons.visibility_off
                                  : Icons.visibility,
                              color: AppConstants.primaryColor,
                            ),
                            onPressed: () {
                              setState(() {
                                showPassword = !showPassword;
                              });
                            },
                          ),
                        ),
                        onChanged: (value) {
                          currentPassFlag = false;
                          setState(() {});
                        },
                        obscureText: showPassword,
                        validator: (value) {
                          currentPassFlag = false;
                          if (value!.isEmpty) {
                            setState(() {
                              currentPassFlag = true;
                            });
                            return null;
                          } else {
                            return null;
                          }
                        },
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        currentPassFlag
                            ? Text(
                                '*Enter your password',
                                style: lbBold.copyWith(
                                  fontSize: 14,
                                  color: AppConstants.redColor,
                                ),
                              )
                            : const SizedBox.shrink(),
                        Flexible(
                          child: _isPassWordCorrect
                              ? _buildPasswordValidationMessage()
                              : const SizedBox.shrink(),
                        ),
                        NetworkAwareTap(
                          onTap: () {
                            forgotPassword();
                            // context.pushNamed('sign-up', extra: {
                            //   "isForgotPassword": true,
                            // });
                          },
                          child: Text('Forgot Password?',
                              style: lbItalic.copyWith(
                                decoration: TextDecoration.underline,
                              )),
                        ),
                      ],
                    ),
                    const SizedBox(height: 15),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "New Password:",
                          style: lbRegular.copyWith(
                            fontSize: 18,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    SizedBox(
                      child: TextFormField(
                        controller: _newPasswordController,
                        style: lbRegular.copyWith(
                          fontSize: 18,
                        ),
                        //focusNode: _passwordFocusNode,
                        onChanged: (value) {
                          newPassFlag = false;
                          setState(() {});
                          _validateNewPassword(_newPasswordController.text);
                        },
                        decoration: InputDecoration(
                          contentPadding: const EdgeInsets.all(10),
                          filled: true,
                          fillColor: Colors.white.withValues(alpha: 0.8),
                          errorStyle: errorMsg,
                          border: const OutlineInputBorder(),
                          suffixIcon: IconButton(
                            icon: Icon(
                              showNewPassword
                                  ? Icons.visibility_off
                                  : Icons.visibility,
                              color: AppConstants.primaryColor,
                            ),
                            onPressed: () {
                              setState(() {
                                showNewPassword = !showNewPassword;
                              });
                            },
                          ),
                        ),
                        obscureText: showNewPassword,
                        validator: (value) {
                          newPassFlag = false;
                          if (value!.isEmpty) {
                            setState(() {
                              newPassFlag = true;
                            });
                            return null;
                          } else {
                            return null;
                          }
                        },
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        newPassFlag
                            ? Text(
                                '*Enter new password',
                                style: lbBold.copyWith(
                                  fontSize: 14,
                                  color: AppConstants.redColor,
                                ),
                              )
                            : const SizedBox.shrink(),
                      ],
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    _buildNewPasswordValidationMessages(),
                    const SizedBox(height: 15),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "Retype Password:",
                          style: lbRegular.copyWith(
                            fontSize: 18,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    TextFormField(
                      controller: _confirmPasswordController,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                      onChanged: (value) {
                        retypePassFlag = false;
                        setState(() {});
                        _validateConfirmPassword(
                            _confirmPasswordController.text);
                      },
                      decoration: InputDecoration(
                        contentPadding: const EdgeInsets.all(10),
                        //labelText: 'Password:',
                        filled: true,
                        fillColor: Colors.white.withValues(alpha: 0.8),
                        errorStyle: errorMsg,
                        border: const OutlineInputBorder(),
                        suffixIcon: IconButton(
                          icon: Icon(
                            showConfirmPassword
                                ? Icons.visibility_off
                                : Icons.visibility,
                            color: AppConstants.primaryColor,
                          ),
                          onPressed: () {
                            setState(() {
                              showConfirmPassword = !showConfirmPassword;
                            });
                          },
                        ),
                      ),
                      obscureText: showConfirmPassword,
                      validator: (value) {
                        retypePassFlag = false;
                        if (value!.isEmpty) {
                          setState(() {
                            retypePassFlag = true;
                          });
                          return null;
                        } else {
                          return null;
                        }
                      },
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        retypePassFlag
                            ? Text(
                                '*Confirm password',
                                style: lbBold.copyWith(
                                  fontSize: 14,
                                  color: AppConstants.redColor,
                                ),
                              )
                            : const SizedBox.shrink(),
                      ],
                    ),
                    _buildConfirmPasswordValidationMessage(),
                    errorMessage.isNotEmpty
                        ? Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                child: Text(
                                  '* $errorMessage',
                                  style: lbRegular.copyWith(
                                    color: AppConstants.redColor,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ],
                          )
                        : SizedBox.shrink(),
                    const SizedBox(
                      height: 25,
                    ),
                    CustomLoaderButton(
                      // loginText: 'Login',
                      buttonWidth:
                          isLoading ? 45.0 : MediaQuery.of(context).size.width,
                      buttonRadius: 30.0,
                      buttonChild: isLoading
                          ? const CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation(Colors.white),
                              strokeWidth: 3.0,
                            )
                          : Text(
                              'Change Password',
                              style: lbBold.copyWith(
                                fontSize: 18,
                                color: AppConstants.primaryColor,
                              ),
                            ),
                      buttonPressed: _submitForm,
                    ),
                    const SizedBox(
                      height: 25,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildValidationMessage(
      String message, bool isValid, bool shouldShow) {
    if (!shouldShow) {
      return const SizedBox.shrink();
    }
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          isValid ? Icons.check_circle : Icons.error,
          color: isValid ? Colors.green : Colors.red,
          size: 18,
        ),
        const SizedBox(width: 8.0),
        Expanded(
          child: Text(
            message,
            style: lbItalic,
          ),
        ),
      ],
    );
  }

  Widget _buildPasswordValidationMessage() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // const SizedBox(height: 10),
        _buildValidationMessage(
          _isPassWordCorrect ? 'Correct' : 'Incorrect',
          _isPassWordCorrect,
          _passwordController.text.isNotEmpty,
        ),
      ],
    );
    /* return Column(
      
      children: _isPassWordCorrect
          ? [
              const SizedBox(
                height: 10,
              ),
              _buildValidationMessage('Correct', _isPassWordCorrect,
                  _passwordController.text.isNotEmpty)
            ]
          : [
              const SizedBox(
                height: 10,
              ),
              _buildValidationMessage('Incorrect', _isPassWordCorrect,
                  _passwordController.text.isNotEmpty)
            ],
    ); */
  }

  Widget _buildNewPasswordValidationMessages() {
    return Column(
      children: [
        // const SizedBox(
        //   height: 10,
        // ),
        _buildValidationMessage(
          '8 Characters',
          _isPasswordValid,
          // _newPasswordController.text.isNotEmpty,
          true,
        ),
        const SizedBox(
          height: 10,
        ),
        _buildValidationMessage(
          '3 of the following: Capital letter, lowercase letter, number or symbol',
          _isPasswordComplex,
          // _newPasswordController.text.isNotEmpty,
          true,
        ),
      ],
    );
  }

  Widget _buildConfirmPasswordValidationMessage() {
    return Column(
      children: [
        _isPasswordMatch
            ? _buildValidationMessage(
                'Matches',
                _isPasswordMatch,
                _confirmPasswordController.text.isNotEmpty,
              )
            : _buildValidationMessage(
                'Password does not match',
                _isPasswordMatch,
                _confirmPasswordController.text.isNotEmpty,
              ),
      ],
    );
  }

  void confirmChangePassword() {
    // setState(() {
    //   confirmationMessage = "Temporary Password has been sent.";
    //   isTempPasswordSent = true;
    // });
    Provider.of<LoginController>(context, listen: false)
        .forgotPassword(userMailId ?? '', context)
        .then((responseMap) {
      if (responseMap["statusCode"] == 200) {
        final token = responseMap['data']['token'];
        context.goNamed(
          'otp',
          extra: {
            'email': userMailId,
            'token': token,
            'isForgotPassword': true,
          },
        );

        isLoading = false;
        setState(() {});
      }
    });
  }

  void handleConfirmChangePassword() {
    setState(() {
      confirmationMessage = "Send temporary password to - $userMailId ?";
    });

    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          content: StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(top: 10.0, right: 10),
                    child: Container(
                      alignment: Alignment.centerRight,
                      child: NetworkAwareTap(
                        onTap: () => context.pop(),
                        child: Image.asset(
                          AppConstants.closePopupImagePath,
                          height: 30,
                          width: 30,
                        ),
                      ),
                    ),
                  ),
                  // const SizedBox(height: 10),
                  SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Text(
                      "Password has been changed",
                      textAlign: TextAlign.center,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 25,
                  ),

                  Padding(
                    padding: const EdgeInsets.only(left: 30.0, right: 20),
                    child: NetworkAwareTap(
                      onTap: () {
                        context.pop();
                        context.pushNamed('Settings');
                      },
                      child: Container(
                        height: 45,
                        width: MediaQuery.of(context).size.width / 3.5,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(49),
                          color: AppConstants.textGreenColor,
                        ),
                        child: Center(
                          child: Text(
                            "Ok",
                            textAlign: TextAlign.center,
                            style: libreBaskervilleRegular.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 30,
                  ),
                ],
              );
            },
          ),
        );
      },
    );
  }

  void forgotPassword() {
    setState(() {
      confirmationMessage = "Send temporary password to - $userMailId ?";
    });

    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          content: StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(top: 10.0, right: 10),
                    child: Container(
                      alignment: Alignment.centerRight,
                      child: NetworkAwareTap(
                        onTap: () => context.pop(),
                        child: Image.asset(
                          AppConstants.closePopupImagePath,
                          height: 30,
                          width: 30,
                        ),
                      ),
                    ),
                  ),
                  // const SizedBox(height: 10),
                  Text(
                    "Forgot Password",
                    textAlign: TextAlign.center,
                    style: lbRegular.copyWith(
                      fontSize: 18,
                    ),
                  ),
                  const SizedBox(height: 25),
                  Padding(
                    padding: const EdgeInsets.only(left: 30.0, right: 20),
                    child: SizedBox(
                      width: MediaQuery.of(context).size.width,
                      child: Text(
                        confirmationMessage,
                        textAlign: TextAlign.center,
                        style: lbRegular.copyWith(
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 25,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 30.0, right: 20),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // if (isTempPasswordSent) ...[
                        //   NetworkAwareTap(
                        //     onTap: () {
                        //       context.pop();
                        //       context.goNamed('Settings');
                        //     },
                        //     child: Container(
                        //       height: 45,
                        //       width: MediaQuery.of(context).size.width / 3.5,
                        //       decoration: BoxDecoration(
                        //         borderRadius: BorderRadius.circular(49),
                        //         color: AppConstants.textGreenColor,
                        //       ),
                        //       child: Center(
                        //         child: Text(
                        //           "Ok",
                        //           textAlign: TextAlign.center,
                        //           style: lbRegular.copyWith(
                        //             fontSize: 18,
                        //           ),
                        //         ),
                        //       ),
                        //     ),
                        //   ),
                        // ] else ...[
                        NetworkAwareTap(
                          onTap: () {
                            setState(() {
                              confirmChangePassword();
                            });
                          },
                          child: Container(
                            height: 45,
                            width: MediaQuery.of(context).size.width / 3,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(49),
                              color: AppConstants.textGreenColor,
                            ),
                            child: Center(
                              child: Text(
                                "Send",
                                textAlign: TextAlign.center,
                                style: lbRegular.copyWith(
                                  fontSize: 18,
                                ),
                              ),
                            ),
                          ),
                        ),
                        NetworkAwareTap(
                          onTap: () => context.pop(),
                          child: Container(
                            height: 45,
                            width: MediaQuery.of(context).size.width / 3,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(49),
                              color: AppConstants.backgroundColor,
                              border: Border.all(
                                color: AppConstants.primaryColor,
                              ),
                            ),
                            child: Center(
                              child: Text(
                                "Cancel",
                                textAlign: TextAlign.center,
                                style: lbRegular.copyWith(
                                  fontSize: 18,
                                ),
                              ),
                            ),
                          ),
                        ),
                        // ]
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 30,
                  ),
                ],
              );
            },
          ),
        );
      },
    );
  }
}
