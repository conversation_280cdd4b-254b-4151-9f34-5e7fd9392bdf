import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/providers/connectivity_provider.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:flutter/material.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

class NoConnectionTag extends StatefulWidget {
  final double bottomPosition;

  const NoConnectionTag({
    super.key,
    required this.bottomPosition,
  });

  @override
  State<NoConnectionTag> createState() => _NoConnectionTagState();
}

class _NoConnectionTagState extends State<NoConnectionTag> {
  final provider = locator<ConnectivityProvider>();
  bool _showNoInternetBanner = false;

  @override
  void initState() {
    _setupConnectivityListener();
    super.initState();
  }

  void _setupConnectivityListener() {
    provider.statusStream.listen((status) {
      if (mounted) {
        setState(() {
          _showNoInternetBanner = status == InternetStatus.disconnected;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedPositioned(
      duration: const Duration(milliseconds: 300),
      bottom: _showNoInternetBanner ? widget.bottomPosition : -150,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(5),
          decoration: BoxDecoration(
            color: AppConstants.backgroundColor,
            borderRadius: BorderRadius.circular(5),
            border: Border.all(color: AppConstants.primaryColor),
          ),
          child: Text(
            'No Internet Connection',
            style: lbRegular.copyWith(
              fontSize: 10,
              color: AppConstants.primaryColor,
              decoration: TextDecoration.none,
            ),
          ),
        ),
      ),
    );
  }
}
