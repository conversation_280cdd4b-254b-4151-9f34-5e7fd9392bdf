import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

import '../../app/core/constants.dart';
import '../../app/core/providers/connectivity_provider.dart';
import '../../app/core/services/setup_locator.dart';
import '../../app/core/utils/text_style.dart';

class ConnectivityLossScreen extends StatelessWidget {
  final VoidCallback? onTryAgain;
  final bool showAppBar;

  const ConnectivityLossScreen({
    super.key,
    this.onTryAgain,
    this.showAppBar = true,
  });

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.sizeOf(context).height;
    final screenWidth = MediaQuery.sizeOf(context).width;

    return Scaffold(
      appBar: showAppBar
          ? AppBar(
              automaticallyImplyLeading: false,
              backgroundColor: AppConstants.textGreenColor,
            )
          : null,
      backgroundColor: Colors.transparent,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          image: DecorationImage(
            image: AssetImage(AppConstants.bgImagePath),
            filterQuality: FilterQuality.high,
            fit: BoxFit.cover,
          ),
        ),
        child: Center(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  'assets/images/no_network.png',
                  height: screenHeight * 0.28,
                  width: screenWidth * 0.61,
                  filterQuality: FilterQuality.high,
                  fit: BoxFit.contain,
                ),
                SizedBox(height: 20),
                Text(
                  'Network Error',
                  style: lbBold.copyWith(
                    fontSize: 20,
                    color: AppConstants.primaryColor,
                  ),
                ),
                SizedBox(height: 10),
                Text(
                  'Check your connection and try again.',
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 14,
                    color: AppConstants.primaryColor,
                  ),
                ),
                SizedBox(height: 40),
                GestureDetector(
                  // onTap: onTryAgain!,
                  onTap: () async {
                    final provider = locator<ConnectivityProvider>();
                    if (provider.status == InternetStatus.connected &&
                        onTryAgain != null) {
                      onTryAgain!();
                    }
                  },
                  child: Container(
                    height: 45,
                    decoration: BoxDecoration(
                      color: AppConstants.textGreenColor,
                      borderRadius: BorderRadius.circular(90),
                    ),
                    child: Center(
                      child: Text(
                        "Try Again",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(fontSize: 18),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class ConnectivityLossSheet extends StatelessWidget {
  final VoidCallback? onTryAgain;

  const ConnectivityLossSheet({
    super.key,
    this.onTryAgain,
  });

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.sizeOf(context).height;
    final screenWidth = MediaQuery.sizeOf(context).width;

    return Container(
      // height: MediaQuery.of(context).size.height * 0.6,
      decoration: BoxDecoration(
        color: AppConstants.backgroundColor,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(
                height: 25,
              ),
              FloatingActionButton.small(
                elevation: 0,
                backgroundColor: AppConstants.textGreenColor,
                onPressed: () {
                  context.pop();
                },
                child: Icon(
                  Icons.keyboard_arrow_down_outlined,
                  color: AppConstants.primaryColor,
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Image.asset(
                'assets/images/no_network.png',
                height: screenHeight * 0.28,
                width: screenWidth * 0.61,
                filterQuality: FilterQuality.high,
                fit: BoxFit.contain,
              ),
              SizedBox(height: 20),
              Text(
                'Network Error',
                style: lbBold.copyWith(
                  fontSize: 20,
                  color: AppConstants.primaryColor,
                ),
              ),
              SizedBox(height: 10),
              Text(
                'Check your connection and try again.',
                textAlign: TextAlign.center,
                style: lbRegular.copyWith(
                  fontSize: 14,
                  color: AppConstants.primaryColor,
                ),
              ),
              SizedBox(height: 40),
              GestureDetector(
                // onTap: onTryAgain!,
                onTap: () async {
                  final provider = locator<ConnectivityProvider>();
                  if (provider.status == InternetStatus.connected &&
                      onTryAgain != null) {
                    context.pop();
                    onTryAgain!();
                  } else {
                    context.pop();
                    await Future.delayed(Duration(milliseconds: 300));
                    if (context.mounted) {
                      _showConnectivityLossSheet(context, onTryAgain);
                    }
                  }
                },
                child: Container(
                  height: 45,
                  decoration: BoxDecoration(
                    color: AppConstants.textGreenColor,
                    borderRadius: BorderRadius.circular(90),
                  ),
                  child: Center(
                    child: Text(
                      "Try Again",
                      textAlign: TextAlign.center,
                      style: lbBold.copyWith(fontSize: 18),
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: MediaQuery.sizeOf(context).height * 0.03,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showConnectivityLossSheet(BuildContext context, VoidCallback? onTap) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      barrierColor: AppConstants.backgroundColor.withOpacity(.5),
      sheetAnimationStyle: AnimationStyle(
        duration: const Duration(milliseconds: 300),
        curve: Curves.elasticIn,
        reverseDuration: const Duration(milliseconds: 300),
        reverseCurve: Curves.elasticOut,
      ),
      builder: (context) => SingleChildScrollView(
        child: ConnectivityLossSheet(
          onTryAgain: onTap,
        ),
      ),
    );
  }
}
