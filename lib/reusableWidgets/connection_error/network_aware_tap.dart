import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/providers/connectivity_provider.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/reusableWidgets/connection_error/connection_lost_screen.dart';
import 'package:flutter/material.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

class NetworkAwareTap extends StatelessWidget {
  final Widget child;
  final VoidCallback onTap;

  const NetworkAwareTap({required this.child, required this.onTap, super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        final provider = locator<ConnectivityProvider>();
        if (provider.status == InternetStatus.disconnected) {
          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            isDismissible: true,
            enableDrag: true,
            backgroundColor: Colors.transparent,
            useRootNavigator: true,
            barrierColor: AppConstants.backgroundColor.withOpacity(.5),
            sheetAnimationStyle: AnimationStyle(
              duration: Duration(milliseconds: 300),
              curve: Curves.elasticIn,
              reverseDuration: Duration(milliseconds: 300),
              reverseCurve: Curves.elasticOut,
            ),
            builder: (context) => SingleChildScrollView(
              child: ConnectivityLossSheet(
                onTryAgain: onTap,
              ),
            ),
          );
          return;
        }
        onTap();
      },
      child: child,
    );
  }
}
