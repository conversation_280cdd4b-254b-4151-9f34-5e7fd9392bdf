import 'dart:async';

import 'package:eljunto/app/core/providers/connectivity_provider.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/reusableWidgets/connection_error/connection_lost_screen.dart';
import 'package:flutter/material.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

class ConnectivityAwareApp extends StatefulWidget {
  final Widget child;
  final GlobalKey<NavigatorState> navigatorKey;
  final Function(bool)? onConnectivityResult;

  const ConnectivityAwareApp({
    super.key,
    required this.child,
    required this.navigatorKey,
    this.onConnectivityResult,
  });

  @override
  State<ConnectivityAwareApp> createState() => _ConnectivityAwareAppState();
}

class _ConnectivityAwareAppState extends State<ConnectivityAwareApp> {
  final _connectivityProvider = locator<ConnectivityProvider>();
  bool _isCheckingConnectivity = false;
  bool _hasInitialConnection = false;

  @override
  void initState() {
    super.initState();
    _checkInitialConnectivity();
  }

  Future<void> _checkInitialConnectivity() async {
    if (_isCheckingConnectivity) return;
    _isCheckingConnectivity = true;

    final status = _connectivityProvider.status;
    if (status == InternetStatus.disconnected) {
      widget.onConnectivityResult?.call(false);
      _showConnectionLostScreen();
    } else {
      widget.onConnectivityResult?.call(true);
      setState(() => _hasInitialConnection = true);
    }
  }

  Future<void> _showConnectionLostScreen() async {
    final context = widget.navigatorKey.currentContext;
    if (context == null) return;

    await Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => ConnectivityLossScreen(
          onTryAgain: () async {
            final status = _connectivityProvider.status;
            if (status == InternetStatus.connected) {
              widget.onConnectivityResult?.call(true);
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => widget.child,
                ),
              );
              setState(() => _hasInitialConnection = true);
            }
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return _hasInitialConnection ? widget.child : const SizedBox.shrink();
  }
}
