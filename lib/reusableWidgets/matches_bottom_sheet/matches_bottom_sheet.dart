import 'dart:developer';
import 'dart:ui';

import 'package:eljunto/app/core/enums/enums.dart';
import 'package:eljunto/app/core/helpers/data_helper.dart';
import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/controller/user_controller.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/invite_popup_dialog.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../app/core/constants.dart';
import '../../app/core/utils/generic_messages.dart';
import '../../app/core/utils/text_style.dart';
import '../../controller/book_club_controller.dart';
import '../../models/book_club_model.dart';
import '../../models/home_model/home_screen1_model/fellow_reader_model.dart';
import '../customDialouge_with_message.dart';

class FellowReaderMatchesBottomSheet extends StatefulWidget {
  final List<int>? currentlyReadMatch;
  final List<int>? tobeReadMatch;
  final List<int>? fivestarMatch;
  final String? userName;
  final int? userId;

  const FellowReaderMatchesBottomSheet({
    super.key,
    this.currentlyReadMatch,
    this.tobeReadMatch,
    this.fivestarMatch,
    this.userName,
    this.userId,
  });

  @override
  State<FellowReaderMatchesBottomSheet> createState() =>
      _FellowReaderMatchesBottomSheetState();
}

class _FellowReaderMatchesBottomSheetState
    extends State<FellowReaderMatchesBottomSheet> {
  /// LIST
  List<FellowReader> currentlyReadList = [];
  List<FellowReader> toBeReadList = [];
  List<FellowReader> fiveStarList = [];

  /// LIMIT VARIABLE
  int currentlyReadLimit = 10;
  int toBeReadLimit = 10;
  int fiveStarLimit = 10;
  int offSet = 0;

  /// LOADING VARIABLE
  bool currentlyReadLoading = false;
  bool toBeReadLoading = false;
  bool fiveStarLoading = false;

  /// COUNT VARIABLE
  int currentlyReadCount = 0;
  int toBeReadCount = 0;
  int fiveStarCount = 0;

  int? loggedinUserId;
  UserController? userController;
  bool isLoading = false;
  List<BookClubModel> clubsLedByUser = [];
  TextEditingController reviewController = TextEditingController();
  bool isButtonLoading = false;
  final _sessionManager = locator<SessionManager>();

  @override
  void initState() {
    userController = Provider.of<UserController>(context, listen: false);
    _initializeUserId();
    super.initState();
  }

  @override
  void dispose() {
    reviewController.dispose();
    super.dispose();
  }

  Future<void> _initializeUserId() async {
    loggedinUserId = _sessionManager.userId;
    isLoading = true;
    setState(() {});
    Future.wait([
      getCurrentlyReadMatch(false),
      getToBeReadMatch(false),
      getFiveStarMatch(false)
    ]).then(
      (value) => setState(
        () {
          isLoading = false;
        },
      ),
    );
  }

  /// CURRENTLY READ MATCH FUNCTION
  Future<void> getCurrentlyReadMatch(bool isMore) async {
    if (currentlyReadList.length <= currentlyReadCount || !isMore) {
      currentlyReadLoading = true;
      if (isMore) {
        currentlyReadLimit += 10;
      }
    }
    await Provider.of<UserController>(context, listen: false)
        .getCurrentlyReadMatch(
      widget.userId ?? 0,
      currentlyReadLimit,
      offSet,
      MatchesFilter.currentlyReadMatch.value,
      widget.currentlyReadMatch ?? [],
      context,
    )
        .then((_) {
      currentlyReadCount = userController?.currentlyReadMatchModel?.count ?? 0;
      currentlyReadList = userController?.currentlyReadMatchModel?.data ?? [];
    }).whenComplete(() {
      currentlyReadLoading = false;
    });
  }

  /// TO BE READ MATCH FUNCTION
  Future<void> getToBeReadMatch(bool isMore) async {
    if (toBeReadList.length <= toBeReadCount || !isMore) {
      toBeReadLoading = true;
      if (isMore) {
        toBeReadLimit += 10;
      }
    }
    await Provider.of<UserController>(context, listen: false)
        .getToBeReadMatch(
      widget.userId ?? 0,
      toBeReadLimit,
      offSet,
      MatchesFilter.toBeReadMatch.value,
      widget.tobeReadMatch ?? [],
      context,
    )
        .then((_) {
      toBeReadCount = userController?.toBeReadMatchModel?.count ?? 0;
      toBeReadList = userController?.toBeReadMatchModel?.data ?? [];
    }).whenComplete(() {
      toBeReadLoading = false;
    });
  }

  /// FIVE STAR MATCH FUNCTION
  Future<void> getFiveStarMatch(bool isMore) async {
    if (fiveStarList.length <= fiveStarCount || !isMore) {
      fiveStarLoading = true;
      if (isMore) {
        fiveStarLimit += 10;
      }
    }
    await Provider.of<UserController>(context, listen: false)
        .getFiveStarMatch(
      widget.userId ?? 0,
      fiveStarLimit,
      offSet,
      MatchesFilter.starMatch.value,
      widget.fivestarMatch ?? [],
      context,
    )
        .then((_) {
      fiveStarCount = userController?.starMatchModel?.count ?? 0;
      fiveStarList = userController?.starMatchModel?.data ?? [];
      log("Star Count : $fiveStarCount");
      log("Star List : ${fiveStarList.length}");
    }).whenComplete(() {
      fiveStarLoading = false;
    });
  }

  int limit = 1000;

  /// INVITE FUNCTION
  Future<void> getClubsLedByUser() async {
    await Provider.of<BookClubController>(context, listen: false)
        .getBookClubs(
      '',
      loggedinUserId ?? 0,
      null,
      context,
      offSet,
      limit,
    )
        .then((responseMap) async {
      if (responseMap["statusCode"] == 200) {
        List<BookClubModel> bookClubList = [];
        // setState(() {
        bookClubList = (responseMap["data"] as List)
            .map((item) => BookClubModel.fromJson(item))
            .toList();
        // });
        var result =
            DataHelper.getClubsLedByUser(bookClubList, loggedinUserId ?? 0);
        //clubsLedByUser = result;
        if (result.isEmpty) {
          showNoClubsLedValidation(
              "To send an invite you need to be a leader of a club. Start your own club to become a leader.");
        } else {
          clubsLedByUser =
              result.where((club) => club.totalVacancies! > 0).toList();

          if (clubsLedByUser.isEmpty) {
            showNoClubsLedValidation(
                "All your groups are currently full. You can invite members when new spots open up.");
          } else {
            showInvitePopup();
          }
        }
      } else {}
    });
  }

  void showNoClubsLedValidation(String msg) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return CustomDialog(
          title: "Invite: ${widget.userName ?? ''}",
          message: msg,
          //"To send an invite you need to be a leader of a club. Start your own club to become a leader.",
          showDoneImage: false,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: ClipRRect(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
            child: BackdropFilter(
              filter: ImageFilter.blur(
                sigmaX: 24,
                sigmaY: 24,
              ),
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: AppConstants.primaryColor,
                    width: 1.5,
                  ),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
              ),
            ),
          ),
        ),
        Column(
          // mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(
              height: 25,
            ),
            isLoading
                ? const Center(
                    child: CircularProgressIndicator(
                      color: AppConstants.primaryColor,
                    ),
                  )
                : NetworkAwareTap(
                    onTap: () {
                      context.pop();
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(40),
                        color: AppConstants.textGreenColor,
                      ),
                      child: const Icon(
                        Icons.keyboard_arrow_down_rounded,
                        color: AppConstants.primaryColor,
                        size: 30,
                      ),
                    ),
                  ),
            !isLoading
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    // mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(
                        height: 25,
                      ),
                      Container(
                        margin: const EdgeInsets.symmetric(
                          horizontal: 20,
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 14, vertical: 14),
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(35),
                          border: Border.all(
                            color: AppConstants.primaryColor,
                            width: 1.5,
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: MarqueeList(
                                children: [
                                  textWidget18(
                                    widget.userName ?? "",
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(
                              width: 5,
                            ),
                            NetworkAwareTap(
                              onTap: () {
                                getClubsLedByUser();
                              },
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 14.0, vertical: 7),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(16),
                                  color: AppConstants.textGreenColor,
                                ),
                                child: Row(
                                  children: [
                                    Text(
                                      "Invite",
                                      overflow: TextOverflow.ellipsis,
                                      style: lbBold.copyWith(
                                        fontSize: 14,
                                        color: AppConstants.primaryColor,
                                      ),
                                    ),
                                    const SizedBox(
                                      width: 6,
                                    ),
                                    Image.asset(
                                      AppConstants.openToInvitationImagePath,
                                      height: 18,
                                      width: 18,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      currentlyReadList.isNotEmpty
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(
                                  height: 25,
                                ),
                                MarqueeList(
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 20.0),
                                      child: textFontSize18(
                                        'Currently Reading Book/Author Match',
                                      ),
                                    ),
                                  ],
                                ),

                                const SizedBox(
                                  height: 10,
                                ),

                                /// CURRENTLY READING BOOKS/AUTHOR MATCH LIST VIEW BUILDER

                                SingleChildScrollView(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 20),
                                  // padding: const EdgeInsets.only(right: 20),
                                  scrollDirection: Axis.horizontal,
                                  child: Consumer<UserController>(
                                    builder: (context, userController, child) {
                                      return Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: List.generate(
                                          currentlyReadLoading
                                              ? currentlyReadList.length + 1
                                              : currentlyReadList.length,
                                          (index) {
                                            if (index ==
                                                    currentlyReadList.length &&
                                                currentlyReadLoading) {
                                              return const Padding(
                                                padding:
                                                    EdgeInsets.only(right: 10),
                                                child:
                                                    CircularProgressIndicator(
                                                  color:
                                                      AppConstants.primaryColor,
                                                ),
                                              );
                                            }
                                            return Container(
                                              width: 200,
                                              margin: const EdgeInsets.only(
                                                  right: 10),
                                              padding: const EdgeInsets.all(14),
                                              decoration: BoxDecoration(
                                                color: Color.fromRGBO(
                                                  255,
                                                  251,
                                                  243,
                                                  .3,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                border: Border.all(
                                                  color:
                                                      AppConstants.primaryColor,
                                                  width: 1.5,
                                                ),
                                              ),
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  MarqueeList(
                                                    children: [
                                                      textFontSize16(
                                                        currentlyReadList[index]
                                                                .bookName ??
                                                            "",
                                                      ),
                                                    ],
                                                  ),
                                                  MarqueeList(
                                                    children: [
                                                      textWidget14(
                                                        currentlyReadList[index]
                                                                .bookAuthor ??
                                                            "",
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            );
                                          },
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ],
                            )
                          : const SizedBox.shrink(),
                      const SizedBox(
                        height: 25,
                      ),

                      /// TO-BE-READ BOOK/AUTHOR MATCH

                      toBeReadList.isNotEmpty
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                MarqueeList(
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 20.0),
                                      child: textFontSize18(
                                        'To-Be-Read Book/Author Match',
                                      ),
                                    ),
                                  ],
                                ),

                                const SizedBox(
                                  height: 10,
                                ),

                                /// TO-BE-READ BOOK/AUTHOR MATCH
                                SingleChildScrollView(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 20),
                                  scrollDirection: Axis.horizontal,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: List.generate(
                                      toBeReadLoading
                                          ? toBeReadList.length + 1
                                          : toBeReadList.length,
                                      (index) {
                                        if (index == toBeReadList.length &&
                                            toBeReadLoading) {
                                          return const Padding(
                                            padding: EdgeInsets.only(right: 10),
                                            child: CircularProgressIndicator(
                                              color: AppConstants.primaryColor,
                                            ),
                                          );
                                        }
                                        return NetworkAwareTap(
                                          onTap: () {},
                                          child: ClipRect(
                                            clipBehavior:
                                                Clip.antiAliasWithSaveLayer,
                                            child: SizedBox(
                                              width: 200,
                                              // key: currentlyReadingBookAuthorItemKeys[
                                              //     index],
                                              // height: 77,
                                              child: Container(
                                                margin: const EdgeInsets.only(
                                                    right: 10),
                                                padding:
                                                    const EdgeInsets.all(14),
                                                decoration: BoxDecoration(
                                                  color: Color.fromRGBO(
                                                    255,
                                                    251,
                                                    243,
                                                    .3,
                                                  ),
                                                  borderRadius:
                                                      BorderRadius.circular(10),
                                                  border: Border.all(
                                                    color: AppConstants
                                                        .primaryColor,
                                                    width: 1.5,
                                                  ),
                                                ),
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    MarqueeList(
                                                      children: [
                                                        textFontSize16(
                                                          toBeReadList[index]
                                                                  .bookName ??
                                                              "",
                                                        ),
                                                      ],
                                                    ),
                                                    MarqueeList(
                                                      children: [
                                                        textWidget14(
                                                          toBeReadList[index]
                                                                  .bookAuthor ??
                                                              "",
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ),

                                const SizedBox(
                                  height: 25,
                                ),
                              ],
                            )
                          : const SizedBox.shrink(),

                      /// 5 STAR AUTHOR REVIEW MATCH SECTION

                      fiveStarList.isNotEmpty
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                MarqueeList(
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 20.0),
                                      child: textFontSize18(
                                        "5-Star Author Review Match",
                                      ),
                                    ),
                                  ],
                                ),

                                const SizedBox(
                                  height: 10,
                                ),

                                /// CURRENTLY READING BOOKS/AUTHOR MATCH LIST VIEW BUILDER
                                SingleChildScrollView(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 20),
                                  scrollDirection: Axis.horizontal,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: List.generate(
                                      fiveStarLoading
                                          ? fiveStarList.length + 1
                                          : fiveStarList.length,
                                      (index) {
                                        if (index == fiveStarList.length &&
                                            fiveStarLoading) {
                                          return const Padding(
                                            padding: EdgeInsets.only(right: 10),
                                            child: CircularProgressIndicator(
                                              color: AppConstants.primaryColor,
                                            ),
                                          );
                                        }
                                        return NetworkAwareTap(
                                          onTap: () {},
                                          child: ClipRect(
                                            clipBehavior:
                                                Clip.antiAliasWithSaveLayer,
                                            child: SizedBox(
                                              width: 200,
                                              child: Container(
                                                margin: const EdgeInsets.only(
                                                    right: 10),
                                                padding:
                                                    const EdgeInsets.all(14),
                                                decoration: BoxDecoration(
                                                  color: Color.fromRGBO(
                                                    255,
                                                    251,
                                                    243,
                                                    .3,
                                                  ),
                                                  borderRadius:
                                                      BorderRadius.circular(10),
                                                  border: Border.all(
                                                    color: AppConstants
                                                        .primaryColor,
                                                    width: 1.5,
                                                  ),
                                                ),
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    MarqueeList(
                                                      children: [
                                                        textFontSize16(
                                                          fiveStarList[index]
                                                                  .bookName ??
                                                              "",
                                                        ),
                                                      ],
                                                    ),
                                                    MarqueeList(
                                                      children: [
                                                        textWidget14(
                                                          fiveStarList[index]
                                                                  .bookAuthor ??
                                                              "",
                                                        ),
                                                      ],
                                                    ),
                                                    const SizedBox(
                                                      height: 25,
                                                    ),
                                                    starShow(index),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ),
                              ],
                            )
                          : const SizedBox.shrink(),

                      const SizedBox(
                        height: 25,
                      ),
                    ],
                  )
                : const SizedBox.shrink(),
          ],
        ),
      ],
    );
  }

  Widget textFontSize18(String title) {
    return Text(
      title,
      overflow: TextOverflow.ellipsis,
      style: lbRegular.copyWith(
        color: AppConstants.primaryColor,
        fontSize: 18,
      ),
    );
  }

  Widget textWidget18(String bookName) {
    return Text(
      bookName,
      overflow: TextOverflow.ellipsis,
      style: lbBold.copyWith(
        fontSize: 18,
        color: AppConstants.primaryColor,
      ),
    );
  }

  Widget textFontSize16(String bookName) {
    return Text(
      bookName,
      overflow: TextOverflow.ellipsis,
      style: lbBold.copyWith(
        fontSize: 16,
        color: AppConstants.primaryColor,
      ),
    );
  }

  Widget textWidget14(String bookAuthor) {
    return Text(
      bookAuthor,
      overflow: TextOverflow.ellipsis,
      style: lbRegular.copyWith(
        fontSize: 14,
        color: AppConstants.primaryColor,
      ),
    );
  }

  Widget starShow(int index) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        (fiveStarList[index].review?.isNotEmpty ?? false)
            ? NetworkAwareTap(
                onTap: () {
                  readReviewFunction(
                    fiveStarList[index].bookName,
                    fiveStarList[index].bookAuthor,
                    fiveStarList[index].review,
                  );
                },
                child: Text(
                  "Review",
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  style: lbItalic.copyWith(
                    fontSize: 12,
                    decoration: TextDecoration.underline,
                  ),
                ),
              )
            : RatingBar(
                ignoreGestures: true,
                itemCount: 5,
                itemSize: 20,
                allowHalfRating: true,
                initialRating: fiveStarList[index].ratings ?? 0,
                minRating: 0,
                ratingWidget: RatingWidget(
                  full: const Icon(
                    Icons.star,
                    color: AppConstants.textGreenColor,
                  ),
                  half: const Icon(
                    Icons.star_half,
                    color: AppConstants.textGreenColor,
                  ),
                  empty: const Icon(
                    Icons.star_border_outlined,
                    color: AppConstants.textGreenColor,
                  ),
                ),
                onRatingUpdate: (double value) {},
              ),
        const SizedBox(
          width: 10,
        ),
        (fiveStarList[index].review?.isNotEmpty ?? false)
            ? RatingBar(
                ignoreGestures: true,
                itemCount: 5,
                itemSize: 20,
                allowHalfRating: true,
                initialRating: fiveStarList[index].ratings ?? 0,
                minRating: 0,
                ratingWidget: RatingWidget(
                  full: const Icon(
                    Icons.star,
                    color: AppConstants.textGreenColor,
                  ),
                  half: const Icon(
                    Icons.star_half,
                    color: AppConstants.textGreenColor,
                  ),
                  empty: const Icon(
                    Icons.star_border_outlined,
                    color: AppConstants.textGreenColor,
                  ),
                ),
                onRatingUpdate: (double value) {},
              )
            : const SizedBox.shrink(),
      ],
    );
  }

  Future<void> readReviewFunction(
      String? bookName, String? bookAuthor, String? review) async {
    await showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        reviewController.clear();
        reviewController.text = review ?? '';
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Text(
                      "Review:",
                      textAlign: TextAlign.center,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          "$bookName, $bookAuthor",
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: TextFormField(
                    controller: reviewController,
                    readOnly: true,
                    maxLines: 4,
                    // maxLength: 2000,
                    style: lbRegular.copyWith(
                      fontSize: 12,
                    ),
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.all(10),
                      fillColor: AppConstants.backgroundColor,
                      filled: true,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(5),
                        borderSide: const BorderSide(
                          color: AppConstants.primaryColor,
                          width: 1.5,
                        ),
                      ),
                      counterStyle: lbRegular.copyWith(
                        fontSize: 14,
                      ),
                      hintText: "No review",
                      hintStyle: lbRegular.copyWith(
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                NetworkAwareTap(
                  onTap: () async {
                    if (context.mounted) {
                      context.pop();
                    }
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3.5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Ok",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  bool bookClubValidation = false;
  bool invitationMsgValidation = false;

  clearValidationMessage() {
    setState(() {
      bookClubValidation = false;
      invitationMsgValidation = false;
    });
  }

  void showInvitePopup() {
    clearValidationMessage();

    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) => InvitePopupDialog(
        userName: widget.userName ?? '',
        clubsLedByUser: clubsLedByUser,
        onCancel: () => context.pop(),
        onInvite: (bookClubId, invitationMsg) async {
          Future.delayed(
            const Duration(milliseconds: 500),
            () => context.pop(),
          );
          await confirmInviteRequest(bookClubId, invitationMsg);
        },
      ),
    );
  }

  Future<void> confirmInviteRequest(
      int bookClubId, String invitationMsg) async {
    String responseMessage = '';
    bool showDoneImg = false;
    final Map<String, dynamic> payload = {
      "bookClubId": bookClubId,
      "userId": widget.userId,
      "userType": ClubMemberType.member,
      "initiatedBy": loggedinUserId ?? 0,
      "invitationMessage": invitationMsg
    };

    try {
      final responseMap =
          await Provider.of<BookClubController>(context, listen: false)
              .addMember(payload, context);

      if (responseMap["statusCode"] == 200) {
        responseMessage = GenericMessages.inviteRequestSuccess;
        showDoneImg = true;
      } else {
        responseMessage = responseMap['error'];
        showDoneImg = false;
      }
    } catch (e) {
      responseMessage = "Failed to submit request: $e";
    } finally {
      if (context.mounted) context.pop();
    }

    if (mounted) {
      showDialog(
        barrierColor: Colors.white60,
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return CustomDialog(
            title: "Invite: ${widget.userName ?? ''}",
            message: responseMessage,
            showDoneImage: showDoneImg,
          );
        },
      ).then((_) => context.pop());
    }
  }
}
