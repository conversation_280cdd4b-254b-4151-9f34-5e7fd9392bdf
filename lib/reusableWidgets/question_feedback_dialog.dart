import 'package:eljunto/app/core/constants.dart';
import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/app/core/utils/text_style.dart';
import 'package:eljunto/controller/user_controller.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/customDialouge_with_message.dart';
import 'package:eljunto/reusableWidgets/custom_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

class QuestionFeedbackDialog extends StatefulWidget {
  final bool isBookQuery;

  const QuestionFeedbackDialog({
    super.key,
    this.isBookQuery = false,
  });

  @override
  State createState() => _QuestionFeedbackDialogState();
}

class _QuestionFeedbackDialogState extends State<QuestionFeedbackDialog> {
  final TextEditingController _feedbackController = TextEditingController();
  int? loggedInUserId;
  final _sessionManager = locator<SessionManager>();

  @override
  void initState() {
    super.initState();
    _initializeUserId();
  }

  Future<void> _initializeUserId() async {
    loggedInUserId = _sessionManager.userId;
    // if (userId != null) {
    //   loggedInUserId = userId;
    // } else {}
  }

  Map<String, dynamic> createPayload() {
    final feedback = _feedbackController.text;
    if (widget.isBookQuery) {
      return {
        "userId": loggedInUserId,
        "bookFeedback": feedback,
      };
    } else {
      return {
        "userId": loggedInUserId,
        "questionOrFeedback": feedback,
      };
    }
  }

  Future<void> _submitFeedback() async {
    final payload = createPayload();
    try {
      /* await Provider.of<UserController>(context, listen: false)
          .postQuestionFeedback(payload)
          .then((value) {
        if (value) {
          showDialog(
            barrierColor: Colors.white60,
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) {
              return const CustomDialog(
                title: "Questions & Feedback",
                subMessage: "Thanks!",
                message: "We’ll get back to you ASAP!",
              );
            },
          );
        }
      }); */

      final success = await Provider.of<UserController>(context, listen: false)
          .postQuestionFeedback(payload);

      // Only show dialog if the widget is still mounted
      if (mounted && success) {
        final _title =
            widget.isBookQuery ? "Can’t find a book?" : "Questions & Feedback";
        context.pop();
        showDialog(
          barrierColor: Colors.white60,
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return CustomDialog(
              title: _title,
              subMessage: "Thanks!",
              message: "We’ll get back to you ASAP!",
            );
          },
        );
      }
    } catch (e) {
      print('Error: $e');
    } finally {}
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      actionsPadding: const EdgeInsets.only(right: 10),
      insetPadding: const EdgeInsets.all(25),
      contentPadding: EdgeInsets.zero,
      backgroundColor: AppConstants.backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: const BorderSide(
          color: AppConstants.popUpBorderColor,
          width: 1.5,
        ),
      ),
      surfaceTintColor: Colors.white,
      actions: [
        Consumer<UserController>(builder: (context, provider, child) {
          return Column(
            children: [
              NetworkAwareTap(
                onTap: () {
                  context.pop();
                },
                child: Container(
                  alignment: Alignment.centerRight,
                  padding: const EdgeInsets.only(
                    top: 10,
                  ),
                  child: Image.asset(
                    AppConstants.closePopupImagePath,
                    height: 30,
                    width: 30,
                  ),
                ),
              ),
              Text(
                widget.isBookQuery
                    ? "Can’t find a book?"
                    : "Questions & Feedback",
                textAlign: TextAlign.center,
                style: lbRegular.copyWith(
                  fontSize: 18,
                ),
              ),
              const SizedBox(height: 30),
              Padding(
                padding: const EdgeInsets.only(left: 32, right: 20),
                child: SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Text(
                    widget.isBookQuery
                        ? "Kindly provide the title and author. We'll get it in here lickety-split."
                        : "Questions? Other feedback? Please let us know! We'll get back to you ASAP.",
                    style: lbRegular.copyWith(
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Padding(
                padding: const EdgeInsets.only(left: 30.0, right: 20),
                child: TextFormField(
                  controller: _feedbackController,
                  maxLines: 5,
                  style: lbRegular.copyWith(
                    fontSize: 12,
                  ),
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.all(10),
                    filled: true,
                    fillColor: const Color.fromRGBO(255, 255, 255, 1),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(5),
                      borderSide: const BorderSide(
                        color: AppConstants.primaryColor,
                        width: 1.5,
                      ),
                    ),
                  ),
                ),
              ),
              provider.apiMessage.isNotEmpty
                  ? const SizedBox(
                      height: 10,
                    )
                  : const SizedBox.shrink(),
              provider.apiMessage.isNotEmpty
                  ? Padding(
                      padding: const EdgeInsets.only(left: 30, right: 20),
                      child: Align(
                        alignment: Alignment.topLeft,
                        child: Text(
                          provider.apiMessage,
                          style: lbBold.copyWith(
                            fontSize: 14,
                            color: AppConstants.redColor,
                          ),
                        ),
                      ),
                    )
                  : const SizedBox.shrink(),
              const SizedBox(
                height: 25,
              ),
              Padding(
                padding: const EdgeInsets.only(left: 30.0, right: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    NetworkAwareTap(
                      onTap: () {
                        _submitFeedback();
                      },
                      child: Container(
                        height: 45,
                        width: MediaQuery.of(context).size.width / 3.2,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(49),
                          color: AppConstants.textGreenColor,
                        ),
                        child: Center(
                          child: Text(
                            "Submit",
                            textAlign: TextAlign.center,
                            style: libreBaskervilleRegular.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ),
                      ),
                    ),
                    NetworkAwareTap(
                      onTap: () {
                        context.pop();
                      },
                      child: Container(
                        height: 45,
                        width: MediaQuery.of(context).size.width / 3.2,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(49),
                          border: Border.all(
                            color: AppConstants.popUpBorderColor,
                          ),
                          color: AppConstants.backgroundColor,
                        ),
                        child: Center(
                          child: Text(
                            "Cancel",
                            textAlign: TextAlign.center,
                            style: libreBaskervilleRegular.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 30,
              ),
            ],
          );
        })
      ],
    );
  }

  /* @override
  void dispose() {
    _feedbackController.dispose();
    super.dispose();
  } */
}
