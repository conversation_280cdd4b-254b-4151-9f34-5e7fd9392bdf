import 'dart:developer';

import 'package:eljunto/app/core/enums/enums.dart';
import 'package:eljunto/app/core/services/session_manager.dart';
import 'package:eljunto/app/core/services/setup_locator.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../app/core/constants.dart';
import '../app/core/utils/generic_messages.dart';
import '../app/core/utils/text_style.dart';
import '../controller/book_club_controller.dart';
import 'customDialouge_with_message.dart';
import 'custom_button.dart';

class ClubJoinPopup extends StatefulWidget {
  final int? bookClubId;
  final String? bookClubName;
  final String? memberRequestPrompt;

  const ClubJoinPopup({
    super.key,
    this.bookClubId,
    this.bookClubName,
    this.memberRequestPrompt,
  });

  @override
  State<ClubJoinPopup> createState() => _ClubJoinPopupState();
}

class _ClubJoinPopupState extends State<ClubJoinPopup> {
  TextEditingController requestController = TextEditingController();
  bool showValidationMessage = false;
  final _sessionManager = locator<SessionManager>();

  @override
  void dispose() {
    requestController.dispose();
    super.dispose();
  }

  bool isButtonLoading = false;
  String responseMessage = '';
  String? userName;

  Future<bool> confirmJoinRequest(String requestMessage) async {
    int? loggedInUserId = _sessionManager.userId;
    userName = _sessionManager.userName;

    bool showDoneImg = false;
    final Map<String, dynamic> payload = {
      "bookClubId": widget.bookClubId,
      "userId": loggedInUserId,
      "userType": ClubMemberType.member.value,
      "initiatedBy": loggedInUserId,
      "requestMessage": requestMessage
    };

    try {
      final responseMap =
          await Provider.of<BookClubController>(context, listen: false)
              .addMember(payload, context);

      if (responseMap["statusCode"] == 200) {
        responseMessage = GenericMessages.joinRequestSuccess;
        showDoneImg = true;
      } else {
        responseMessage = responseMap['error'];
        showDoneImg = false;
      }
    } catch (e) {
      responseMessage = "Failed to submit request: $e";
    }
    log("Boolean value : $showDoneImg");
    return showDoneImg;
  }

  void clearValidationMessage() {
    setState(() {
      showValidationMessage = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SingleChildScrollView(
        child: GestureDetector(
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: AlertDialog(
            actionsPadding: const EdgeInsets.only(right: 10),
            insetPadding: const EdgeInsets.all(25),
            contentPadding: EdgeInsets.zero,
            backgroundColor: AppConstants.backgroundColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
              side: const BorderSide(
                color: AppConstants.popUpBorderColor,
                width: 1.5,
              ),
            ),
            surfaceTintColor: Colors.white,
            actions: [
              Column(
                children: [
                  NetworkAwareTap(
                    onTap: () {
                      context.pop();
                    },
                    child: Container(
                      alignment: Alignment.centerRight,
                      padding: const EdgeInsets.only(top: 10),
                      child: Image.asset(
                        AppConstants.closePopupImagePath,
                        height: 30,
                        width: 30,
                      ),
                    ),
                  ),
                  Text(
                    "Request to Join",
                    textAlign: TextAlign.center,
                    style: lbRegular.copyWith(
                      fontSize: 18,
                    ),
                  ),
                  const SizedBox(height: 30),
                  Padding(
                    padding: const EdgeInsets.only(left: 30, right: 20),
                    child: SizedBox(
                      // height: 65,
                      width: MediaQuery.of(context).size.width,
                      child: Text(
                        widget.memberRequestPrompt ?? '',
                        style: lbRegular.copyWith(
                          fontSize: 12,
                          // height: 22.32,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 30, right: 20),
                    child: Stack(
                      clipBehavior: Clip.none,
                      children: [
                        TextFormField(
                          controller: requestController,
                          maxLines: 6,
                          maxLength: 500,
                          style: lbRegular.copyWith(
                            fontSize: 12,
                          ),
                          decoration: InputDecoration(
                            contentPadding: const EdgeInsets.all(10),
                            filled: true,
                            fillColor: const Color.fromRGBO(255, 255, 255, 1),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(5),
                              borderSide: const BorderSide(
                                color: AppConstants.primaryColor,
                                width: 1.5,
                              ),
                            ),
                            counterStyle: lbRegular.copyWith(
                              fontSize: 14,
                              color: AppConstants.primaryColor,
                            ),
                          ),
                          onChanged: (value) {
                            setState(() {
                              showValidationMessage = false;
                            });
                          },
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        Positioned(
                          top: 130,
                          child: showValidationMessage
                              ? Text(
                                  '*Enter a message',
                                  overflow: TextOverflow.ellipsis,
                                  style: lbRegular.copyWith(
                                    fontSize: 14,
                                    color: AppConstants.redColor,
                                  ),
                                )
                              : const SizedBox.shrink(),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 25,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 30, right: 20),
                    child: Row(
                      mainAxisAlignment: isButtonLoading
                          ? MainAxisAlignment.center
                          : MainAxisAlignment.spaceBetween,
                      children: [
                        CustomLoaderButton(
                          // loginText: 'Login',
                          buttonWidth: isButtonLoading
                              ? 45.0
                              : MediaQuery.of(context).size.width / 3.2,
                          buttonRadius: 30.0,
                          buttonChild: isButtonLoading
                              ? const CircularProgressIndicator(
                                  valueColor:
                                      AlwaysStoppedAnimation(Colors.white),
                                  strokeWidth: 3.0,
                                )
                              : Text(
                                  "Submit",
                                  style: lbBold.copyWith(
                                    fontSize: 18,
                                    color: AppConstants.primaryColor,
                                  ),
                                ),
                          buttonPressed: () async {
                            if (requestController.text.isEmpty) {
                              setState(() {
                                showValidationMessage = true;
                              });
                            } else {
                              setState(() {
                                isButtonLoading = true;
                              });
                              clearValidationMessage();
                              // context.pop();
                              await confirmJoinRequest(requestController.text)
                                  .then((value) {
                                if (context.mounted) {
                                  context.pop();
                                }
                                if (value) {
                                  showRequestFunction();
                                } else {
                                  if (context.mounted) {
                                    showDialog(
                                      barrierColor: Colors.white60,
                                      context: context,
                                      barrierDismissible: false,
                                      builder: (BuildContext context) {
                                        return CustomDialog(
                                          title:
                                              "Join: ${widget.bookClubName ?? ''}",
                                          message: responseMessage,
                                          showDoneImage: value,
                                        );
                                      },
                                    );
                                  }
                                }
                              });
                            }
                            setState(() {
                              isButtonLoading = false;
                            });
                          },
                        ),
                        !isButtonLoading
                            ? NetworkAwareTap(
                                onTap: () {
                                  context.pop();
                                },
                                child: Container(
                                  height: 45,
                                  width:
                                      MediaQuery.of(context).size.width / 3.2,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(49),
                                    border: Border.all(
                                      color: AppConstants.popUpBorderColor,
                                    ),
                                    color: AppConstants.backgroundColor,
                                  ),
                                  child: Center(
                                    child: Text(
                                      "Cancel",
                                      textAlign: TextAlign.center,
                                      style: lbBold.copyWith(
                                        fontSize: 18,
                                      ),
                                    ),
                                  ),
                                ),
                              )
                            : const SizedBox.shrink(),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 30,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> showRequestFunction() async {
    await showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Column(
                    children: [
                      Image.asset(
                        AppConstants.requestDoneImg,
                        filterQuality: FilterQuality.high,
                        fit: BoxFit.cover,
                        height: 79,
                        width: 79,
                      ),
                      const SizedBox(
                        height: 25,
                      ),
                      SizedBox(
                        width: MediaQuery.of(context).size.width,
                        child: Text(
                          "Your request to join has been submitted to the club leader",
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 18,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                NetworkAwareTap(
                  onTap: () async {
                    // final bookId = currentbookCaseList?[index].bookId;
                    // await deleteBook(bookId).then(
                    //   (value) {},
                    // );
                    // setState(() {});
                    if (context.mounted) {
                      context.pop();
                      context.pop();
                    }
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3.5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Ok",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }
}
